
# 端口映射说明:
# - 外部端口8082 -> 内部端口80 (HTTP)
# - 外部端口8081 -> 内部端口443 (HTTPS)
# 
# 项目结构:
# - 网站根目录: /opt/haoju/web/
# - Vue项目1: /opt/haoju/web/op/
# - Vue项目2: /opt/haoju/web/hot/
# - API服务: localhost:6007

# 上游API服务器配置
upstream interface-server {
    server 127.0.0.1:6007;
    keepalive 32;
}

# HTTPS服务器配置 (监听443端口)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name 51.onefind.club;
    
    # 网站根目录
    root /opt/haoju/web;
    index index.html index.htm;

    # 确保包含MIME类型配置
    include /etc/nginx/mime.types;

    # 添加额外的MIME类型支持
    location ~* \.css$ {
        add_header Content-Type text/css;
    }
    location ~* \.js$ {
        add_header Content-Type application/javascript;
    }
    location ~* \.map$ {
        add_header Content-Type application/json;
    }
    
    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/51.onefind.club.pem;
    ssl_certificate_key /etc/nginx/ssl/51.onefind.club.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # 安全头配置
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 隐藏Nginx版本信息
    server_tokens off;
    
    # 访问日志配置
    access_log /var/log/nginx/haoju_access.log combined;
    error_log /var/log/nginx/haoju_error.log warn;
    
    # API代理配置 - 处理后端API请求
    location /interface-server/ {
        proxy_pass http://interface-server;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 跨域配置
        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        add_header Access-Control-Expose-Headers "Content-Length,Content-Range" always;
        
        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin * always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
        
        # 代理超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Vue项目1配置 - op
    location /op/ {
        alias /opt/haoju/web/op/;
        try_files $uri $uri/ /op/index.html;

        # 静态资源缓存和MIME类型配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;

            # 确保正确的MIME类型
            location ~* \.css$ {
                add_header Content-Type text/css;
            }
            location ~* \.js$ {
                add_header Content-Type application/javascript;
            }
        }
    }

    # 处理/op路径（不带尾部斜杠）的重定向
    location = /op {
        return 301 /op/;
    }
    
    # Vue项目2配置 - hot
    location /hot/ {
        alias /opt/haoju/web/hot/;
        try_files $uri $uri/ /hot/index.html;

        # 静态资源缓存和MIME类型配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;

            # 确保正确的MIME类型
            location ~* \.css$ {
                add_header Content-Type text/css;
            }
            location ~* \.js$ {
                add_header Content-Type application/javascript;
            }
        }
    }

    # 处理/hot路径（不带尾部斜杠）的重定向
    location = /hot {
        return 301 /hot/;
    }
    
    # 根路径配置 - 可以重定向到其中一个项目或显示选择页面
    location = / {
        # 选项1: 重定向到hot项目（使用完整URL保持端口）
        return 301 $scheme://$http_host/hot/;

        # 选项2: 显示项目选择页面 (取消上面的注释，启用下面的配置)
        # try_files /index.html =404;
    }
    
    # 处理根目录下的其他请求
    location / {
        try_files $uri $uri/ =404;
    }
    
    # 安全配置 - 拒绝访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 拒绝访问备份文件
    location ~* \.(bak|backup|old|orig|original|tmp)$ {
        deny all;
    }
    
    # Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        font/opentype;
}

# HTTP服务器配置 (监听80端口) - 强制重定向到HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name 51.onefind.club;

    # 隐藏Nginx版本信息
    server_tokens off;

    # 访问日志
    access_log /var/log/nginx/haoju_http_access.log combined;

    # 设置HTTPS端口变量
    set $https_port 8081;

    # 强制301重定向到HTTPS的指定端口
    return 301 https://$server_name:$https_port$request_uri;
}

# 默认服务器配置 - 处理未匹配的域名
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name _;
    return 444;  # 关闭连接，不响应未匹配域名的HTTP请求
}
server {
    listen 443 ssl default_server;
    listen [::]:443 ssl default_server;
    server_name _;
    
    # 注意：默认HTTPS服务器仍需证书，可使用自签名证书
    ssl_certificate /etc/nginx/ssl/51.onefind.club.pem;
    ssl_certificate_key /etc/nginx/ssl/51.onefind.club.key;
    
    return 444;
}
