# MySQL 高可用主从复制架构在 ARM64 银河麒麟 V10 SP3 系统部署教程

## 目录
1. [系统环境检查和准备工作](#1-系统环境检查和准备工作)
2. [MySQL基础安装](#2-mysql基础安装)
3. [主节点(Master)配置](#3-主节点master配置)
4. [从节点(Slave)配置](#4-从节点slave配置)
5. [主从复制配置](#5-主从复制配置)
6. [半同步复制配置](#6-半同步复制配置)
7. [故障转移机制配置](#7-故障转移机制配置)
8. [Spring Boot集成配置](#8-spring-boot集成配置)
9. [监控和健康检查](#9-监控和健康检查)
10. [验证和测试](#10-验证和测试)
11. [故障恢复和维护](#11-故障恢复和维护)
12. [ARM架构和麒麟系统特殊注意事项](#12-arm架构和麒麟系统特殊注意事项)
13. [常见问题排查](#13-常见问题排查)

---

## 1. 系统环境检查和准备工作

### 1.1 服务器规划

**架构说明：**
- **主节点(Master)**: ************* - 处理所有写操作和部分读操作
- **从节点(Slave)**: ************* - 处理读操作，作为备份节点

**⚠️ 重要提醒：**
- 确保两台服务器网络互通
- 建议使用相同的硬件配置
- 确保时间同步

### 1.2 检查系统信息
```bash
# 在两台服务器上都执行以下命令
# 检查操作系统版本
cat /etc/os-release

# 检查处理器架构
uname -m

# 检查内核版本
uname -r

# 检查可用内存（建议至少4GB）
free -h

# 检查磁盘空间（建议至少50GB）
df -h

# 检查网络连通性
ping -c 3 *************  # 从从节点ping主节点
ping -c 3 *************  # 从主节点ping从节点
```

**预期输出示例：**
- 架构应显示：`aarch64`
- 操作系统：`Kylin Linux V10 SP3`
- 内存：≥ 4GB
- 磁盘空间：≥ 50GB

### 1.3 时间同步配置
```bash
# 安装NTP服务
sudo yum install -y chrony

# 启动并设置开机自启
sudo systemctl start chronyd
sudo systemctl enable chronyd

# 检查时间同步状态
chrony sources -v

# 手动同步时间（如果需要）
sudo chrony makestep
```

### 1.4 防火墙和SELinux配置
```bash
# 配置防火墙允许MySQL端口
sudo firewall-cmd --permanent --add-port=3306/tcp
sudo firewall-cmd --reload

# 检查SELinux状态
getenforce

# 如果是Enforcing，建议设置为Permissive（生产环境需要配置SELinux策略）
sudo setenforce 0
sudo sed -i 's/SELINUX=enforcing/SELINUX=permissive/g' /etc/selinux/config
```

### 1.5 系统优化配置
```bash
# 修改系统限制
sudo tee -a /etc/security/limits.conf << EOF
mysql soft nofile 65536
mysql hard nofile 65536
mysql soft nproc 32768
mysql hard nproc 32768
EOF

# 修改内核参数
sudo tee -a /etc/sysctl.conf << EOF
# MySQL优化参数
vm.swappiness = 1
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_fin_timeout = 30
EOF

# 应用内核参数
sudo sysctl -p
```

---

## 2. MySQL基础安装

### 2.1 安装MySQL 8.0

**🇨🇳 中国大陆用户镜像源配置（重要）**

```bash
# 使用阿里云镜像源（推荐）
sudo yum install -y https://mirrors.aliyun.com/mysql/yum/mysql80-community-release-el8-1.noarch.rpm

# 清理缓存并更新
sudo yum clean all
sudo yum makecache

# 安装MySQL服务器
sudo yum install -y mysql-community-server mysql-community-client
```

### 2.2 创建数据目录
```bash
# 创建数据目录
sudo mkdir -p /var/lib/mysql
sudo mkdir -p /var/log/mysql
sudo mkdir -p /etc/mysql/conf.d

# 设置权限
sudo chown -R mysql:mysql /var/lib/mysql
sudo chown -R mysql:mysql /var/log/mysql
sudo chown -R mysql:mysql /etc/mysql
```

### 2.3 初始化MySQL
```bash
# 初始化MySQL数据库
sudo mysqld --initialize --user=mysql --datadir=/var/lib/mysql

# 查看临时密码
sudo grep 'temporary password' /var/log/mysqld.log
```

**⚠️ 重要：** 记录临时密码，后续配置需要使用

---

## 3. 主节点(Master)配置

### 3.1 主节点配置文件

创建主节点专用配置文件：

```bash
sudo tee /etc/my.cnf << 'EOF'
[mysqld]
# 基础配置
port = 3306
socket = /var/lib/mysql/mysql.sock
datadir = /var/lib/mysql
pid-file = /var/run/mysqld/mysqld.pid
user = mysql

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 主从复制配置 - 主节点
server-id = 1
log-bin = mysql-bin
binlog-format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# 半同步复制配置
rpl_semi_sync_master_enabled = 1
rpl_semi_sync_master_timeout = 1000

# 性能优化配置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 1
sync_binlog = 1

# 连接配置
max_connections = 1000
max_connect_errors = 100000
wait_timeout = 28800
interactive_timeout = 28800

# 查询缓存
query_cache_type = 1
query_cache_size = 128M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 错误日志
log-error = /var/log/mysql/error.log

# 安全配置
skip-name-resolve
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
socket = /var/lib/mysql/mysql.sock
EOF
```

### 3.2 启动主节点MySQL服务
```bash
# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 检查服务状态
sudo systemctl status mysqld

# 检查端口监听
sudo netstat -tlnp | grep :3306
```

### 3.3 主节点安全配置
```bash
# 使用临时密码登录并修改root密码
mysql -u root -p

# 在MySQL命令行中执行：
ALTER USER 'root'@'localhost' IDENTIFIED BY 'YourStrongPassword123!';
FLUSH PRIVILEGES;

# 创建复制用户
CREATE USER 'replication'@'*************' IDENTIFIED BY 'ReplicationPassword123!';
GRANT REPLICATION SLAVE ON *.* TO 'replication'@'*************';
FLUSH PRIVILEGES;

# 查看主节点状态
SHOW MASTER STATUS;
```

**⚠️ 重要：** 记录 `SHOW MASTER STATUS` 的输出结果，包括 `File` 和 `Position` 值

---

## 4. 从节点(Slave)配置

### 4.1 从节点配置文件

创建从节点专用配置文件：

```bash
sudo tee /etc/my.cnf << 'EOF'
[mysqld]
# 基础配置
port = 3306
socket = /var/lib/mysql/mysql.sock
datadir = /var/lib/mysql
pid-file = /var/run/mysqld/mysqld.pid
user = mysql

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 主从复制配置 - 从节点
server-id = 2
log-bin = mysql-bin
binlog-format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# 从节点特殊配置
relay-log = relay-bin
relay-log-index = relay-bin.index
read_only = 1
super_read_only = 1

# 半同步复制配置
rpl_semi_sync_slave_enabled = 1

# 性能优化配置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
sync_binlog = 0

# 连接配置
max_connections = 1000
max_connect_errors = 100000
wait_timeout = 28800
interactive_timeout = 28800

# 查询缓存
query_cache_type = 1
query_cache_size = 128M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 错误日志
log-error = /var/log/mysql/error.log

# 安全配置
skip-name-resolve
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
socket = /var/lib/mysql/mysql.sock
EOF
```

### 4.2 启动从节点MySQL服务
```bash
# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 检查服务状态
sudo systemctl status mysqld

# 检查端口监听
sudo netstat -tlnp | grep :3306
```

### 4.3 从节点安全配置
```bash
# 使用临时密码登录并修改root密码
mysql -u root -p

# 在MySQL命令行中执行：
ALTER USER 'root'@'localhost' IDENTIFIED BY 'YourStrongPassword123!';
FLUSH PRIVILEGES;
```

---

## 5. 主从复制配置

### 5.1 配置从节点连接主节点

在从节点MySQL中执行：

```sql
# 停止从节点复制（如果之前有配置）
STOP SLAVE;

# 配置主节点连接信息
CHANGE MASTER TO
    MASTER_HOST='*************',
    MASTER_USER='replication',
    MASTER_PASSWORD='ReplicationPassword123!',
    MASTER_LOG_FILE='mysql-bin.000001',  -- 使用主节点SHOW MASTER STATUS的File值
    MASTER_LOG_POS=154;                  -- 使用主节点SHOW MASTER STATUS的Position值

# 启动从节点复制
START SLAVE;

# 检查从节点状态
SHOW SLAVE STATUS\G
```

**⚠️ 重要检查项：**
- `Slave_IO_Running: Yes`
- `Slave_SQL_Running: Yes`
- `Seconds_Behind_Master: 0` (或很小的数值)

### 5.2 验证主从复制

在主节点创建测试数据：

```sql
# 在主节点执行
CREATE DATABASE test_replication;
USE test_replication;
CREATE TABLE test_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO test_table (name) VALUES ('Test Data 1'), ('Test Data 2');
```

在从节点验证数据同步：

```sql
# 在从节点执行
USE test_replication;
SELECT * FROM test_table;
```

---

## 6. 半同步复制配置

### 6.1 安装半同步复制插件

在主节点执行：

```sql
# 安装主节点半同步插件
INSTALL PLUGIN rpl_semi_sync_master SONAME 'semisync_master.so';

# 启用半同步复制
SET GLOBAL rpl_semi_sync_master_enabled = 1;
SET GLOBAL rpl_semi_sync_master_timeout = 1000;

# 查看半同步状态
SHOW VARIABLES LIKE 'rpl_semi_sync_master%';
SHOW STATUS LIKE 'rpl_semi_sync_master%';
```

在从节点执行：

```sql
# 安装从节点半同步插件
INSTALL PLUGIN rpl_semi_sync_slave SONAME 'semisync_slave.so';

# 启用半同步复制
SET GLOBAL rpl_semi_sync_slave_enabled = 1;

# 重启从节点复制以应用半同步设置
STOP SLAVE IO_THREAD;
START SLAVE IO_THREAD;

# 查看半同步状态
SHOW VARIABLES LIKE 'rpl_semi_sync_slave%';
SHOW STATUS LIKE 'rpl_semi_sync_slave%';
```

### 6.2 验证半同步复制

```sql
# 在主节点查看半同步客户端数量
SHOW STATUS LIKE 'rpl_semi_sync_master_clients';

# 应该显示：rpl_semi_sync_master_clients = 1
```

---

## 7. 故障转移机制配置

### 7.1 安装MHA工具（MySQL Master High Availability）

```bash
# 在两台服务器上安装依赖
sudo yum install -y perl-DBD-MySQL perl-Config-Tiny perl-Log-Dispatch perl-Parallel-ForkManager

# 下载MHA Node
wget https://github.com/yoshinorim/mha4mysql-node/releases/download/v0.58/mha4mysql-node-0.58-0.el7.centos.noarch.rpm

# 安装MHA Node
sudo rpm -ivh mha4mysql-node-0.58-0.el7.centos.noarch.rpm
```

在管理节点（可以是主节点或独立服务器）安装MHA Manager：

```bash
# 下载MHA Manager
wget https://github.com/yoshinorim/mha4mysql-manager/releases/download/v0.58/mha4mysql-manager-0.58-0.el7.centos.noarch.rpm

# 安装MHA Manager
sudo rpm -ivh mha4mysql-manager-0.58-0.el7.centos.noarch.rpm
```

### 7.2 配置SSH免密登录

```bash
# 在所有节点生成SSH密钥
ssh-keygen -t rsa -b 2048 -f ~/.ssh/id_rsa -N ""

# 复制公钥到所有节点（包括自己）
ssh-copy-id root@*************
ssh-copy-id root@*************

# 测试SSH连接
ssh root@************* 'echo "SSH to master OK"'
ssh root@************* 'echo "SSH to slave OK"'
```

### 7.3 创建MHA配置文件

```bash
# 创建MHA配置目录
sudo mkdir -p /etc/mha
sudo mkdir -p /var/log/mha

# 创建MHA配置文件
sudo tee /etc/mha/mysql_ha.cnf << 'EOF'
[server default]
# MySQL用户和密码
user=root
password=YourStrongPassword123!
ssh_user=root
repl_user=replication
repl_password=ReplicationPassword123!

# MHA工作目录
manager_workdir=/var/log/mha
manager_log=/var/log/mha/manager.log
remote_workdir=/tmp

# 故障转移脚本
master_ip_failover_script=/usr/local/bin/master_ip_failover
shutdown_script=""

# 检查复制延迟
ping_interval=3
secondary_check_script=masterha_secondary_check -s *************

[server1]
hostname=*************
port=3306
candidate_master=1

[server2]
hostname=*************
port=3306
candidate_master=1
check_repl_delay=0
EOF
```

### 7.4 创建VIP故障转移脚本

```bash
sudo tee /usr/local/bin/master_ip_failover << 'EOF'
#!/usr/bin/env perl
use strict;
use warnings FATAL => 'all';
use Getopt::Long;

my (
    $command,        $ssh_user,         $orig_master_host,
    $orig_master_ip, $orig_master_port, $new_master_host,
    $new_master_ip,  $new_master_port
);

my $vip = '*************/24';  # 虚拟IP
my $key = "1";
my $ssh_start_vip = "/sbin/ifconfig eth0:$key $vip";
my $ssh_stop_vip = "/sbin/ifconfig eth0:$key down";

GetOptions(
    'command=s'          => \$command,
    'ssh_user=s'         => \$ssh_user,
    'orig_master_host=s' => \$orig_master_host,
    'orig_master_ip=s'   => \$orig_master_ip,
    'orig_master_port=i' => \$orig_master_port,
    'new_master_host=s'  => \$new_master_host,
    'new_master_ip=s'    => \$new_master_ip,
    'new_master_port=i'  => \$new_master_port,
);

exit &main();

sub main {
    if ( $command eq "stop" || $command eq "stopssh" ) {
        my $exit_code = 1;
        eval {
            print "Disabling the VIP on old master: $orig_master_host \n";
            &stop_vip();
            $exit_code = 0;
        };
        if ($@) {
            warn "Got Error: $@\n";
            exit $exit_code;
        }
        exit $exit_code;
    }
    elsif ( $command eq "start" ) {
        my $exit_code = 10;
        eval {
            print "Enabling the VIP - $vip on the new master - $new_master_host \n";
            &start_vip();
            $exit_code = 0;
        };
        if ($@) {
            warn $@;
            exit $exit_code;
        }
        exit $exit_code;
    }
    elsif ( $command eq "status" ) {
        print "Checking the Status of the script.. OK \n";
        exit 0;
    }
    else {
        &usage();
        exit 1;
    }
}

sub start_vip() {
    `ssh $ssh_user\@$new_master_host \" $ssh_start_vip \"`;
}

sub stop_vip() {
    `ssh $ssh_user\@$orig_master_host \" $ssh_stop_vip \"`;
}

sub usage {
    print
"Usage: master_ip_failover --command=start|stop|stopssh|status --orig_master_host=host --orig_master_ip=ip --orig_master_port=port --new_master_host=host --new_master_ip=ip --new_master_port=port\n";
}
EOF

# 设置执行权限
sudo chmod +x /usr/local/bin/master_ip_failover
```

### 7.5 配置虚拟IP

在当前主节点添加虚拟IP：

```bash
# 添加虚拟IP到主节点
sudo ifconfig eth0:1 *************/24

# 验证VIP配置
ip addr show eth0
```

### 7.6 启动MHA监控

```bash
# 检查MHA配置
masterha_check_ssh --conf=/etc/mha/mysql_ha.cnf
masterha_check_repl --conf=/etc/mha/mysql_ha.cnf

# 启动MHA Manager
nohup masterha_manager --conf=/etc/mha/mysql_ha.cnf > /var/log/mha/manager.log 2>&1 &

# 检查MHA状态
masterha_check_status --conf=/etc/mha/mysql_ha.cnf
```

---

## 8. Spring Boot集成配置

### 8.1 Maven依赖配置

在Spring Boot项目的 `pom.xml` 中添加：

```xml
<dependencies>
    <!-- MySQL驱动 -->
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>8.0.33</version>
    </dependency>

    <!-- Spring Boot数据访问 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>

    <!-- HikariCP连接池 -->
    <dependency>
        <groupId>com.zaxxer</groupId>
        <artifactId>HikariCP</artifactId>
    </dependency>

    <!-- 动态数据源 -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        <version>3.6.1</version>
    </dependency>
</dependencies>
```

### 8.2 application.yml配置文件

```yaml
spring:
  datasource:
    dynamic:
      primary: master  # 设置默认数据源
      strict: false    # 严格模式，默认false
      datasource:
        # 主数据源（写）
        master:
          url: jdbc:mysql://*************:3306/your_database?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
          username: your_username
          password: your_password
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            pool-name: HikariCP-Master
            minimum-idle: 5
            maximum-pool-size: 20
            auto-commit: true
            idle-timeout: 30000
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1
            validation-timeout: 3000
            leak-detection-threshold: 60000

        # 从数据源（读）
        slave:
          url: jdbc:mysql://*************:3306/your_database?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
          username: your_username
          password: your_password
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            pool-name: HikariCP-Slave
            minimum-idle: 5
            maximum-pool-size: 15
            auto-commit: true
            idle-timeout: 30000
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1
            validation-timeout: 3000
            leak-detection-threshold: 60000
            read-only: true

  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true

# 日志配置
logging:
  level:
    com.baomidou.dynamic.datasource: debug
    org.springframework.jdbc: debug
```

### 8.3 读写分离配置类

```java
package com.example.config;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.stereotype.Component;

/**
 * 数据源切换配置
 */
@Component
public class DataSourceConfig {

    /**
     * 主数据源 - 用于写操作
     */
    public static final String MASTER = "master";

    /**
     * 从数据源 - 用于读操作
     */
    public static final String SLAVE = "slave";
}
```

### 8.4 Service层读写分离示例

```java
package com.example.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.example.config.DataSourceConfig;
import com.example.entity.User;
import com.example.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class UserService {

    @Autowired
    private UserRepository userRepository;

    /**
     * 查询操作 - 使用从数据库
     */
    @DS(DataSourceConfig.SLAVE)
    @Transactional(readOnly = true)
    public List<User> findAllUsers() {
        return userRepository.findAll();
    }

    /**
     * 根据ID查询 - 使用从数据库
     */
    @DS(DataSourceConfig.SLAVE)
    @Transactional(readOnly = true)
    public User findUserById(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    /**
     * 保存操作 - 使用主数据库
     */
    @DS(DataSourceConfig.MASTER)
    @Transactional
    public User saveUser(User user) {
        return userRepository.save(user);
    }

    /**
     * 更新操作 - 使用主数据库
     */
    @DS(DataSourceConfig.MASTER)
    @Transactional
    public User updateUser(User user) {
        return userRepository.save(user);
    }

    /**
     * 删除操作 - 使用主数据库
     */
    @DS(DataSourceConfig.MASTER)
    @Transactional
    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }
}
```

### 8.5 故障转移配置

```yaml
# 在application.yml中添加故障转移配置
spring:
  datasource:
    dynamic:
      datasource:
        master:
          # 主数据源故障转移配置
          url: jdbc:mysql://*************:3306,*************:3306/your_database?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&autoReconnect=true&failOverReadOnly=false&maxReconnects=3
          hikari:
            connection-timeout: 5000
            validation-timeout: 3000
            max-lifetime: 600000
```

---

## 9. 监控和健康检查

### 9.1 MySQL状态监控脚本

```bash
# 创建监控脚本目录
sudo mkdir -p /usr/local/scripts

# 创建主从状态检查脚本
sudo tee /usr/local/scripts/mysql_replication_check.sh << 'EOF'
#!/bin/bash

# MySQL连接参数
MYSQL_USER="root"
MYSQL_PASSWORD="YourStrongPassword123!"
MASTER_HOST="*************"
SLAVE_HOST="*************"

# 日志文件
LOG_FILE="/var/log/mysql_replication_check.log"

# 获取当前时间
CURRENT_TIME=$(date '+%Y-%m-%d %H:%M:%S')

# 检查主节点状态
echo "[$CURRENT_TIME] 检查主节点状态..." >> $LOG_FILE
MASTER_STATUS=$(mysql -h$MASTER_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD -e "SHOW MASTER STATUS\G" 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "[$CURRENT_TIME] 主节点状态正常" >> $LOG_FILE
    echo "$MASTER_STATUS" >> $LOG_FILE
else
    echo "[$CURRENT_TIME] 主节点连接失败!" >> $LOG_FILE
    exit 1
fi

# 检查从节点状态
echo "[$CURRENT_TIME] 检查从节点状态..." >> $LOG_FILE
SLAVE_STATUS=$(mysql -h$SLAVE_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD -e "SHOW SLAVE STATUS\G" 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "[$CURRENT_TIME] 从节点状态正常" >> $LOG_FILE

    # 检查复制状态
    IO_RUNNING=$(echo "$SLAVE_STATUS" | grep "Slave_IO_Running:" | awk '{print $2}')
    SQL_RUNNING=$(echo "$SLAVE_STATUS" | grep "Slave_SQL_Running:" | awk '{print $2}')
    SECONDS_BEHIND=$(echo "$SLAVE_STATUS" | grep "Seconds_Behind_Master:" | awk '{print $2}')

    echo "[$CURRENT_TIME] IO线程状态: $IO_RUNNING" >> $LOG_FILE
    echo "[$CURRENT_TIME] SQL线程状态: $SQL_RUNNING" >> $LOG_FILE
    echo "[$CURRENT_TIME] 复制延迟: $SECONDS_BEHIND 秒" >> $LOG_FILE

    if [ "$IO_RUNNING" != "Yes" ] || [ "$SQL_RUNNING" != "Yes" ]; then
        echo "[$CURRENT_TIME] 警告：复制线程异常!" >> $LOG_FILE
        exit 1
    fi

    if [ "$SECONDS_BEHIND" != "NULL" ] && [ "$SECONDS_BEHIND" -gt 60 ]; then
        echo "[$CURRENT_TIME] 警告：复制延迟过大!" >> $LOG_FILE
    fi
else
    echo "[$CURRENT_TIME] 从节点连接失败!" >> $LOG_FILE
    exit 1
fi

echo "[$CURRENT_TIME] 主从复制状态检查完成" >> $LOG_FILE
EOF

# 设置执行权限
sudo chmod +x /usr/local/scripts/mysql_replication_check.sh
```

### 9.2 配置定时监控

```bash
# 添加定时任务
sudo crontab -e

# 添加以下内容（每5分钟检查一次）
*/5 * * * * /usr/local/scripts/mysql_replication_check.sh

# 查看定时任务
sudo crontab -l
```

### 9.3 性能监控脚本

```bash
sudo tee /usr/local/scripts/mysql_performance_monitor.sh << 'EOF'
#!/bin/bash

MYSQL_USER="root"
MYSQL_PASSWORD="YourStrongPassword123!"
MYSQL_HOST="*************"
LOG_FILE="/var/log/mysql_performance.log"
CURRENT_TIME=$(date '+%Y-%m-%d %H:%M:%S')

echo "[$CURRENT_TIME] MySQL性能监控开始" >> $LOG_FILE

# 检查连接数
CONNECTIONS=$(mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD -e "SHOW STATUS LIKE 'Threads_connected';" | tail -1 | awk '{print $2}')
echo "[$CURRENT_TIME] 当前连接数: $CONNECTIONS" >> $LOG_FILE

# 检查慢查询
SLOW_QUERIES=$(mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD -e "SHOW STATUS LIKE 'Slow_queries';" | tail -1 | awk '{print $2}')
echo "[$CURRENT_TIME] 慢查询数量: $SLOW_QUERIES" >> $LOG_FILE

# 检查InnoDB缓冲池使用率
BUFFER_POOL_SIZE=$(mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD -e "SHOW STATUS LIKE 'Innodb_buffer_pool_pages_total';" | tail -1 | awk '{print $2}')
BUFFER_POOL_FREE=$(mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD -e "SHOW STATUS LIKE 'Innodb_buffer_pool_pages_free';" | tail -1 | awk '{print $2}')
BUFFER_POOL_USAGE=$(echo "scale=2; ($BUFFER_POOL_SIZE - $BUFFER_POOL_FREE) * 100 / $BUFFER_POOL_SIZE" | bc)
echo "[$CURRENT_TIME] InnoDB缓冲池使用率: ${BUFFER_POOL_USAGE}%" >> $LOG_FILE

echo "[$CURRENT_TIME] MySQL性能监控完成" >> $LOG_FILE
EOF

sudo chmod +x /usr/local/scripts/mysql_performance_monitor.sh
```

---

## 10. 验证和测试

### 10.1 主从复制功能测试

```bash
# 在主节点创建测试数据库和表
mysql -u root -p << 'EOF'
CREATE DATABASE ha_test;
USE ha_test;

CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO users (username, email) VALUES
('user1', '<EMAIL>'),
('user2', '<EMAIL>'),
('user3', '<EMAIL>');

SELECT * FROM users;
EOF
```

```bash
# 在从节点验证数据同步
mysql -u root -p << 'EOF'
USE ha_test;
SELECT * FROM users;
SELECT COUNT(*) as total_users FROM users;
EOF
```

### 10.2 读写分离测试

创建测试脚本：

```bash
sudo tee /usr/local/scripts/test_read_write_split.sh << 'EOF'
#!/bin/bash

MASTER_HOST="*************"
SLAVE_HOST="*************"
MYSQL_USER="root"
MYSQL_PASSWORD="YourStrongPassword123!"

echo "=== 测试写操作（主节点）==="
mysql -h$MASTER_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD -e "
USE ha_test;
INSERT INTO users (username, email) VALUES ('test_write', '<EMAIL>');
SELECT 'Write operation completed on MASTER' as status;
"

echo "=== 等待复制同步 ==="
sleep 2

echo "=== 测试读操作（从节点）==="
mysql -h$SLAVE_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD -e "
USE ha_test;
SELECT * FROM users WHERE username = 'test_write';
SELECT 'Read operation completed on SLAVE' as status;
"

echo "=== 测试完成 ==="
EOF

sudo chmod +x /usr/local/scripts/test_read_write_split.sh

# 运行测试
/usr/local/scripts/test_read_write_split.sh
```

### 10.3 故障转移测试

```bash
# 模拟主节点故障
sudo systemctl stop mysqld  # 在主节点执行

# 检查MHA是否自动进行故障转移
tail -f /var/log/mha/manager.log

# 验证VIP是否切换到从节点
ip addr show eth0

# 测试应用连接
mysql -h************* -u root -p -e "SELECT 'Failover test successful' as status;"
```

### 10.4 性能压力测试

```bash
# 安装sysbench
sudo yum install -y sysbench

# 创建测试数据
sysbench oltp_read_write \
    --mysql-host=************* \
    --mysql-port=3306 \
    --mysql-user=root \
    --mysql-password=YourStrongPassword123! \
    --mysql-db=ha_test \
    --tables=4 \
    --table-size=100000 \
    prepare

# 运行读写混合测试
sysbench oltp_read_write \
    --mysql-host=************* \
    --mysql-port=3306 \
    --mysql-user=root \
    --mysql-password=YourStrongPassword123! \
    --mysql-db=ha_test \
    --tables=4 \
    --table-size=100000 \
    --threads=16 \
    --time=300 \
    --report-interval=10 \
    run

# 清理测试数据
sysbench oltp_read_write \
    --mysql-host=************* \
    --mysql-port=3306 \
    --mysql-user=root \
    --mysql-password=YourStrongPassword123! \
    --mysql-db=ha_test \
    --tables=4 \
    cleanup
```

---

## 11. 故障恢复和维护

### 11.1 主节点故障恢复

当原主节点恢复后，需要将其配置为新的从节点：

```bash
# 1. 在原主节点修改配置文件
sudo tee -a /etc/my.cnf << 'EOF'
# 修改server-id避免冲突
server-id = 3
read_only = 1
super_read_only = 1
EOF

# 2. 重启MySQL服务
sudo systemctl restart mysqld

# 3. 配置为从节点
mysql -u root -p << 'EOF'
STOP SLAVE;
RESET SLAVE ALL;

CHANGE MASTER TO
    MASTER_HOST='*************',  -- 新的主节点
    MASTER_USER='replication',
    MASTER_PASSWORD='ReplicationPassword123!',
    MASTER_AUTO_POSITION=1;

START SLAVE;
SHOW SLAVE STATUS\G
EOF
```

### 11.2 数据一致性检查

```bash
# 安装pt-table-checksum工具
sudo yum install -y percona-toolkit

# 检查主从数据一致性
pt-table-checksum \
    --host=************* \
    --user=root \
    --password=YourStrongPassword123! \
    --databases=ha_test \
    --replicate=percona.checksums

# 如果发现不一致，使用pt-table-sync修复
pt-table-sync \
    --host=************* \
    --user=root \
    --password=YourStrongPassword123! \
    --databases=ha_test \
    --print
```

### 11.3 备份策略

```bash
# 创建备份脚本
sudo tee /usr/local/scripts/mysql_backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
MYSQL_USER="root"
MYSQL_PASSWORD="YourStrongPassword123!"
SLAVE_HOST="*************"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 从从节点进行备份（减少对主节点的影响）
mysqldump -h$SLAVE_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD \
    --single-transaction \
    --routines \
    --triggers \
    --all-databases \
    --master-data=2 \
    | gzip > $BACKUP_DIR/mysql_backup_$DATE.sql.gz

# 删除7天前的备份
find $BACKUP_DIR -name "mysql_backup_*.sql.gz" -mtime +7 -delete

echo "Backup completed: mysql_backup_$DATE.sql.gz"
EOF

sudo chmod +x /usr/local/scripts/mysql_backup.sh

# 添加定时备份任务（每天凌晨2点）
sudo crontab -e
# 添加：0 2 * * * /usr/local/scripts/mysql_backup.sh
```

### 11.4 日志轮转配置

```bash
# 配置MySQL日志轮转
sudo tee /etc/logrotate.d/mysql << 'EOF'
/var/log/mysql/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 640 mysql mysql
    postrotate
        if test -x /usr/bin/mysqladmin && \
           /usr/bin/mysqladmin ping &>/dev/null
        then
           /usr/bin/mysqladmin flush-logs
        fi
    endscript
}
EOF
```

---

## 12. ARM架构和麒麟系统特殊注意事项

### 12.1 ARM64架构优化配置

**⚠️ ARM64特殊配置：**

```bash
# ARM64架构MySQL优化参数
sudo tee -a /etc/my.cnf << 'EOF'
# ARM64架构特殊优化
innodb_use_native_aio = 0
innodb_flush_method = O_DIRECT
innodb_io_capacity = 200
innodb_io_capacity_max = 400

# ARM64内存管理优化
innodb_buffer_pool_instances = 4
innodb_page_cleaners = 4
innodb_read_io_threads = 8
innodb_write_io_threads = 8

# ARM64 CPU优化
innodb_spin_wait_delay = 6
innodb_sync_spin_loops = 30
EOF
```

### 12.2 麒麟系统兼容性配置

```bash
# 检查麒麟系统版本兼容性
cat /etc/kylin-release

# 麒麟系统特殊的SELinux配置
sudo setsebool -P mysql_connect_any 1
sudo setsebool -P httpd_can_network_connect_db 1

# 麒麟系统防火墙配置
sudo firewall-cmd --permanent --add-service=mysql
sudo firewall-cmd --permanent --add-port=3306/tcp
sudo firewall-cmd --reload
```

### 12.3 性能调优建议

```bash
# ARM64系统内核参数优化
sudo tee -a /etc/sysctl.conf << 'EOF'
# ARM64 MySQL优化
vm.swappiness = 1
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 65536 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728
EOF

sudo sysctl -p
```

### 12.4 ARM64编译优化

如果需要从源码编译MySQL：

```bash
# ARM64编译参数
cmake . -DCMAKE_INSTALL_PREFIX=/usr/local/mysql \
        -DMYSQL_DATADIR=/var/lib/mysql \
        -DSYSCONFDIR=/etc \
        -DWITH_MYISAM_STORAGE_ENGINE=1 \
        -DWITH_INNOBASE_STORAGE_ENGINE=1 \
        -DWITH_MEMORY_STORAGE_ENGINE=1 \
        -DWITH_READLINE=1 \
        -DMYSQL_UNIX_ADDR=/var/lib/mysql/mysql.sock \
        -DMYSQL_TCP_PORT=3306 \
        -DENABLED_LOCAL_INFILE=1 \
        -DWITH_PARTITION_STORAGE_ENGINE=1 \
        -DEXTRA_CHARSETS=all \
        -DDEFAULT_CHARSET=utf8mb4 \
        -DDEFAULT_COLLATION=utf8mb4_general_ci \
        -DCMAKE_C_FLAGS="-march=armv8-a -mtune=cortex-a72" \
        -DCMAKE_CXX_FLAGS="-march=armv8-a -mtune=cortex-a72"
```

---

## 13. 常见问题排查

### 13.1 主从复制问题

**问题1：从节点IO线程无法启动**

```bash
# 检查错误日志
sudo tail -f /var/log/mysql/error.log

# 常见解决方案
mysql -u root -p << 'EOF'
STOP SLAVE;
RESET SLAVE;
CHANGE MASTER TO
    MASTER_HOST='*************',
    MASTER_USER='replication',
    MASTER_PASSWORD='ReplicationPassword123!',
    MASTER_LOG_FILE='mysql-bin.000001',
    MASTER_LOG_POS=154;
START SLAVE;
EOF
```

**问题2：复制延迟过大**

```bash
# 检查从节点状态
mysql -u root -p -e "SHOW SLAVE STATUS\G" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master)"

# 优化建议
sudo tee -a /etc/my.cnf << 'EOF'
# 减少复制延迟
slave_parallel_type = LOGICAL_CLOCK
slave_parallel_workers = 4
slave_preserve_commit_order = 1
EOF
```

**问题3：主从数据不一致**

```bash
# 使用pt-table-checksum检查
pt-table-checksum --host=************* --user=root --password=YourStrongPassword123!

# 修复数据不一致
pt-table-sync --host=************* --user=root --password=YourStrongPassword123! --execute
```

### 13.2 连接问题

**问题1：连接数过多**

```bash
# 检查当前连接数
mysql -u root -p -e "SHOW STATUS LIKE 'Threads_connected';"

# 查看连接详情
mysql -u root -p -e "SHOW PROCESSLIST;"

# 优化配置
sudo tee -a /etc/my.cnf << 'EOF'
max_connections = 2000
max_connect_errors = 100000
wait_timeout = 28800
interactive_timeout = 28800
EOF
```

**问题2：连接超时**

```bash
# 检查网络连通性
ping -c 3 *************
telnet ************* 3306

# 优化连接参数
sudo tee -a /etc/my.cnf << 'EOF'
connect_timeout = 60
net_read_timeout = 60
net_write_timeout = 60
EOF
```

### 13.3 性能问题

**问题1：查询速度慢**

```bash
# 启用慢查询日志
mysql -u root -p << 'EOF'
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
SET GLOBAL log_queries_not_using_indexes = 'ON';
EOF

# 分析慢查询
mysqldumpslow /var/log/mysql/slow.log
```

**问题2：内存使用过高**

```bash
# 检查内存使用
mysql -u root -p -e "SHOW STATUS LIKE 'Innodb_buffer_pool%';"

# 优化内存配置
sudo tee -a /etc/my.cnf << 'EOF'
innodb_buffer_pool_size = 70%  # 系统内存的70%
query_cache_size = 128M
tmp_table_size = 64M
max_heap_table_size = 64M
EOF
```

### 13.4 ARM64特殊问题

**问题1：编译错误**

```bash
# 安装ARM64开发工具
sudo yum groupinstall -y "Development Tools"
sudo yum install -y cmake gcc-c++ ncurses-devel openssl-devel

# 设置编译环境
export CC=gcc
export CXX=g++
export CFLAGS="-march=armv8-a"
export CXXFLAGS="-march=armv8-a"
```

**问题2：性能不佳**

```bash
# ARM64性能优化
sudo tee -a /etc/my.cnf << 'EOF'
# ARM64特殊优化
innodb_flush_log_at_trx_commit = 2
sync_binlog = 0
innodb_doublewrite = 0
EOF
```

### 13.5 故障转移问题

**问题1：MHA无法启动**

```bash
# 检查MHA配置
masterha_check_ssh --conf=/etc/mha/mysql_ha.cnf
masterha_check_repl --conf=/etc/mha/mysql_ha.cnf

# 检查SSH免密登录
ssh root@************* 'echo "SSH OK"'
ssh root@************* 'echo "SSH OK"'
```

**问题2：VIP切换失败**

```bash
# 手动测试VIP脚本
/usr/local/bin/master_ip_failover --command=status

# 检查网络接口
ip addr show eth0
```

### 13.6 日志分析

**重要日志文件位置：**

```bash
# MySQL错误日志
tail -f /var/log/mysql/error.log

# MySQL慢查询日志
tail -f /var/log/mysql/slow.log

# MHA管理日志
tail -f /var/log/mha/manager.log

# 系统日志
tail -f /var/log/messages

# 复制监控日志
tail -f /var/log/mysql_replication_check.log
```

**⚠️ 紧急故障处理流程：**

1. **确认故障范围** - 检查主从节点状态
2. **保护数据安全** - 停止写入操作
3. **执行故障转移** - 手动或自动切换
4. **验证服务恢复** - 测试读写功能
5. **通知相关人员** - 发送故障通知
6. **故障后分析** - 分析根本原因

---

## 总结

本文档详细介绍了在ARM64银河麒麟V10 SP3系统上部署MySQL高可用主从复制架构的完整流程，包括：

✅ **完成的配置项：**
- 双节点主从复制架构
- 半同步复制确保数据一致性
- MHA自动故障转移机制
- Spring Boot读写分离集成
- 完整的监控和健康检查
- 详细的故障恢复流程
- ARM64架构特殊优化

✅ **关键特性：**
- 数据实时同步
- 自动故障转移
- 读写分离
- 性能监控
- 备份策略

**🔧 后续维护建议：**
- 定期检查复制状态
- 监控系统性能指标
- 执行定期备份
- 测试故障转移机制
- 更新安全补丁

通过本文档的配置，您将获得一个高可用、高性能的MySQL数据库集群，能够满足生产环境的可靠性要求。
