<mxfile host="65bd71144e">
    <diagram name="Haoju System Architecture" id="haoju-system-arch">
        <mxGraphModel dx="1336" dy="926" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title" value="Haoju 系统架构图 (ARM64 Kylin Linux)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="400" y="20" width="400" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="client" value="用户/客户端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
                    <mxGeometry x="480" y="70" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="serverA-container" value="8h-32g (主应用服务器 + 负载均衡器)&lt;br&gt;ARM64 Kylin Linux" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;verticalAlign=top;fontStyle=1;fontSize=14;dashed=0;" parent="1" vertex="1">
                    <mxGeometry x="40" y="160" width="360" height="440" as="geometry"/>
                </mxCell>
                <mxCell id="serverB-container" value="4g-32G-1 (从库 + 接口服务器)&lt;br&gt;ARM64 Kylin Linux" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;verticalAlign=top;fontStyle=1;fontSize=14;" parent="1" vertex="1">
                    <mxGeometry x="440" y="160" width="280" height="440" as="geometry"/>
                </mxCell>
                <mxCell id="serverC-container" value="4g-32G-2(主库 + 消息处理服务器)&lt;br&gt;ARM64 Kylin Linux" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;verticalAlign=top;fontStyle=1;fontSize=14;" parent="1" vertex="1">
                    <mxGeometry x="760" y="160" width="360" height="440" as="geometry"/>
                </mxCell>
                <mxCell id="nginx-lb" value="Nginx 负载均衡器&#xa;(/opt/haoju/nginx)&#xa;端口: 80, 443" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="80" y="210" width="280" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="interface-server-A" value="interface-server 接口服务&lt;br&gt;(/opt/haoju/interface-server)&lt;br&gt;端口: 6007" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="80" y="280" width="280" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tomcat-kpbase" value="Tomcat + kpbase 应用&#xa;(/opt/haoju/tomcat)&#xa;端口: 8005, 8009, 8080" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="80" y="350" width="280" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="frontend-A" value="前端项目: op, hot&lt;br&gt;(/opt/haoju/web)&lt;br&gt;通过 Nginx 访问" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="80" y="420" width="130" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="redis-A" value="Redis 缓存&lt;br&gt;&lt;br&gt;端口: 6379" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="220" y="420" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="activemq-A" value="ActiveMQ 消息队列&#xa;(/opt/haoju/activemq)&#xa;端口: 61616, 8161" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="80" y="490" width="280" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="mysql-slave" value="MySQL 从库 (只读)&lt;br&gt;&lt;br&gt;端口: 3306" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="480" y="210" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="interface-server-B" value="interface-server 接口服务&#xa;(/opt/haoju/interface-server)&#xa;端口: 8080" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="480" y="280" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="frontend-B" value="前端项目: hot&lt;br&gt;(/opt/haoju/web)&lt;br&gt;通过 Nginx 访问" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="480" y="350" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="mysql-master" value="MySQL 主库 (读写)&#xa;(/opt/haoju/mysql)&#xa;端口: 3306" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="800" y="212" width="280" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="activemq-log" value="activemq-log 日志消息处理&#xa;(/opt/haoju/activemq-log)&#xa;Java 应用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="800" y="352" width="280" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="activemq-money" value="activemq-money 金融消息处理&#xa;(/opt/haoju/activemq-money)&#xa;Java 应用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="800" y="422" width="280" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="timeDefine" value="timeDefine 定时器服务&#xa;(/opt/haoju/timeDefine)&#xa;Java 应用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="800" y="492" width="280" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="legend" value="图例" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="40" y="620" width="240" height="180" as="geometry"/>
                </mxCell>
                <mxCell id="legend1" value="&lt;font color=&quot;#ff0000&quot;&gt;——&lt;/font&gt; 数据库写操作 / 主从复制" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="legend" vertex="1">
                    <mxGeometry y="30" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="legend2" value="&lt;font color=&quot;#0000ff&quot;&gt;- - -&lt;/font&gt; 数据库读操作" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="legend" vertex="1">
                    <mxGeometry y="60" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="legend3" value="&lt;font color=&quot;#ff00ff&quot;&gt;——&lt;/font&gt; 消息队列通信" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="legend" vertex="1">
                    <mxGeometry y="90" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="legend4" value="&lt;font color=&quot;#00cccc&quot;&gt;——&lt;/font&gt; 缓存访问" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="legend" vertex="1">
                    <mxGeometry y="120" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="legend5" value="—— API调用/负载均衡" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="legend" vertex="1">
                    <mxGeometry y="150" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="notes" value="说明：&#xa;1. 所有服务安装在 /opt/haoju 目录下&#xa;2. 所有服务器基于 ARM64 Kylin Linux 环境&#xa;3. A 服务器 Nginx 负载均衡分发请求到 A、B 服务器&#xa;4. MySQL 采用主从复制架构，C 服务器为主库，B 服务器为从库&#xa;5. ActiveMQ 在 A 和 C 服务器上部署，实现消息同步&#xa;6. 接口服务通过读写分离提高性能" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;align=left;" parent="1" vertex="1">
                    <mxGeometry x="320" y="620" width="400" height="180" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="activemq 消息处理&lt;br&gt;(/opt/haoju/activemq)&lt;br&gt;Java 应用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
                    <mxGeometry x="800" y="282" width="280" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>