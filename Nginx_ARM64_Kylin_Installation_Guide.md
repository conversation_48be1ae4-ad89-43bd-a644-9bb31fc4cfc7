# Nginx 在 ARM64 银河麒麟 V10 SP3 系统安装教程

## 目录
1. [系统环境检查和准备工作](#1-系统环境检查和准备工作)
2. [Nginx安装](#2-nginx安装)
3. [基础配置文件设置](#3-基础配置文件设置)
4. [服务启动和开机自启动](#4-服务启动和开机自启动)
5. [SSL/HTTPS配置](#5-sslhttps配置)
6. [反向代理配置](#6-反向代理配置)
7. [负载均衡配置](#7-负载均衡配置)
8. [性能优化配置](#8-性能优化配置)
9. [安全配置](#9-安全配置)
10. [验证安装和功能测试](#10-验证安装和功能测试)
11. [ARM架构和麒麟系统特殊注意事项](#11-arm架构和麒麟系统特殊注意事项)
12. [常见问题排查](#12-常见问题排查)

---

## 1. 系统环境检查和准备工作

### 1.1 检查系统信息
```bash
# 检查操作系统版本
cat /etc/os-release

# 检查处理器架构
uname -m

# 检查内核版本
uname -r

# 检查可用内存
free -h

# 检查磁盘空间
df -h
```

**预期输出示例：**
- 架构应显示：`aarch64`
- 操作系统：`Kylin Linux V10 SP3`

### 1.2 检查现有Web服务
```bash
# 检查是否已安装Nginx
nginx -v

# 检查是否有其他Web服务运行
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :443

# 检查已安装的Web服务器包
rpm -qa | grep -E "(nginx|httpd|apache)"
```

### 1.3 更新系统包
```bash
# 更新包管理器缓存
sudo yum update -y

# 安装必要的工具
sudo yum install -y wget curl vim net-tools lsof
```

### 1.4 检查端口占用
```bash
# 检查HTTP端口80是否被占用
sudo netstat -tlnp | grep :80

# 检查HTTPS端口443是否被占用
sudo netstat -tlnp | grep :443

# 如果端口被占用，查看占用进程
sudo lsof -i :80
sudo lsof -i :443
```

---

## 2. Nginx安装

### 2.1 中国大陆用户镜像源配置（重要）

**🇨🇳 强烈建议中国大陆用户使用国内镜像源！**

Nginx官方源在中国大陆访问速度较慢，建议使用国内镜像源：

#### 2.1.1 配置EPEL源（推荐）
```bash
# 安装EPEL源
sudo yum install -y epel-release

# 备份原始源配置
sudo cp /etc/yum.repos.d/epel.repo /etc/yum.repos.d/epel.repo.backup

# 使用阿里云EPEL镜像源
sudo sed -i 's|^#baseurl=https://download.fedoraproject.org/pub|baseurl=https://mirrors.aliyun.com|' /etc/yum.repos.d/epel.repo
sudo sed -i 's|^metalink|#metalink|' /etc/yum.repos.d/epel.repo

# 清理缓存并更新
sudo yum clean all
sudo yum makecache
```

#### 2.1.2 配置Nginx官方源（可选）
```bash
# 创建Nginx官方源配置文件
sudo tee /etc/yum.repos.d/nginx.repo << 'EOF'
[nginx-stable]
name=nginx stable repo
baseurl=http://nginx.org/packages/centos/$releasever/$basearch/
gpgcheck=1
enabled=1
gpgkey=https://nginx.org/keys/nginx_signing.key
module_hotfixes=true
EOF

# 导入GPG密钥
sudo rpm --import https://nginx.org/keys/nginx_signing.key
```

### 2.2 使用YUM安装Nginx

#### 2.2.1 搜索可用的Nginx包
```bash
# 搜索Nginx相关包
yum search nginx

# 查看Nginx包信息
yum info nginx
```

#### 2.2.2 安装Nginx
```bash
# 安装Nginx主程序
sudo yum install -y nginx

# 验证安装
nginx -v
nginx -V  # 查看编译参数和模块信息
```

**⚠️ 重要提醒：**
- 如果安装失败，请检查网络连接和镜像源配置
- ARM64架构可能需要使用EPEL源中的版本

### 2.3 创建必要的目录和用户
```bash
# 创建Nginx相关目录
sudo mkdir -p /var/log/nginx
sudo mkdir -p /etc/nginx/conf.d
sudo mkdir -p /var/www/html

# 设置目录权限
sudo chown -R nginx:nginx /var/log/nginx
sudo chown -R nginx:nginx /var/www/html

# 检查nginx用户是否存在
id nginx
```

---

## 3. 基础配置文件设置

### 3.1 主配置文件优化

备份原始配置文件：
```bash
# 备份原始配置
sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup
```

创建优化的主配置文件：
```bash
sudo tee /etc/nginx/nginx.conf << 'EOF'
# Nginx主配置文件 - ARM64银河麒麟优化版本
user nginx;
worker_processes auto;  # 自动检测CPU核心数
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# ARM64架构优化
worker_rlimit_nofile 65535;
worker_cpu_affinity auto;

events {
    worker_connections 4096;  # ARM64优化连接数
    use epoll;
    multi_accept on;
    accept_mutex off;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 日志格式定义
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';

    access_log /var/log/nginx/access.log main;

    # 基础优化配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_tokens off;  # 隐藏版本信息

    # 文件上传大小限制
    client_max_body_size 100M;
    client_body_buffer_size 128k;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;

    # Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 包含其他配置文件
    include /etc/nginx/conf.d/*.conf;

    # 默认服务器配置
    server {
        listen 80 default_server;
        listen [::]:80 default_server;
        server_name _;
        root /var/www/html;
        index index.html index.htm;

        # 安全配置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;

        location / {
            try_files $uri $uri/ =404;
        }

        # 静态文件缓存
        location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 隐藏敏感文件
        location ~ /\. {
            deny all;
        }

        # 错误页面
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /var/www/html;
        }
    }
}
EOF
```

### 3.2 创建默认网页
```bash
# 创建默认首页
sudo tee /var/www/html/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nginx - ARM64银河麒麟系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; }
        .info { background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .success { color: #27ae60; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Nginx 安装成功！</h1>
        <div class="info">
            <p><strong>系统信息：</strong></p>
            <ul>
                <li>操作系统：银河麒麟 V10 SP3</li>
                <li>架构：ARM64 (aarch64)</li>
                <li>Web服务器：Nginx</li>
            </ul>
        </div>
        <p class="success">✅ 如果您看到此页面，说明Nginx已经成功安装并运行！</p>
        <p>您可以开始配置您的网站了。配置文件位置：</p>
        <ul>
            <li>主配置文件：<code>/etc/nginx/nginx.conf</code></li>
            <li>站点配置：<code>/etc/nginx/conf.d/</code></li>
            <li>网站根目录：<code>/var/www/html/</code></li>
        </ul>
    </div>
</body>
</html>
EOF

# 设置文件权限
sudo chown nginx:nginx /var/www/html/index.html
```

### 3.3 验证配置文件语法
```bash
# 检查配置文件语法
sudo nginx -t

# 如果有错误，查看详细信息
sudo nginx -T
```

---

## 4. 服务启动和开机自启动

### 4.1 启动Nginx服务
```bash
# 启动Nginx服务
sudo systemctl start nginx

# 检查服务状态
sudo systemctl status nginx

# 设置开机自启动
sudo systemctl enable nginx

# 验证自启动设置
sudo systemctl is-enabled nginx
```

### 4.2 防火墙配置
```bash
# 检查防火墙状态
sudo firewall-cmd --state

# 允许HTTP服务
sudo firewall-cmd --permanent --add-service=http

# 允许HTTPS服务
sudo firewall-cmd --permanent --add-service=https

# 或者直接开放端口
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp

# 重新加载防火墙规则
sudo firewall-cmd --reload

# 查看已开放的服务和端口
sudo firewall-cmd --list-all
```

### 4.3 SELinux配置
```bash
# 检查SELinux状态
getenforce

# 如果SELinux是启用状态，需要设置相关策略
sudo setsebool -P httpd_can_network_connect 1
sudo setsebool -P httpd_can_network_relay 1

# 设置Nginx相关的SELinux上下文
sudo semanage fcontext -a -t httpd_config_t "/etc/nginx(/.*)?"
sudo semanage fcontext -a -t httpd_log_t "/var/log/nginx(/.*)?"
sudo restorecon -Rv /etc/nginx
sudo restorecon -Rv /var/log/nginx
```

### 4.4 服务管理命令
```bash
# 启动服务
sudo systemctl start nginx

# 停止服务
sudo systemctl stop nginx

# 重启服务
sudo systemctl restart nginx

# 重新加载配置（不中断服务）
sudo systemctl reload nginx

# 查看服务状态
sudo systemctl status nginx

# 查看服务日志
sudo journalctl -u nginx -f
```

---

## 5. SSL/HTTPS配置

### 5.1 安装SSL证书工具
```bash
# 安装OpenSSL（通常已预装）
sudo yum install -y openssl

# 创建SSL证书目录
sudo mkdir -p /etc/nginx/ssl
sudo chmod 700 /etc/nginx/ssl
```

### 5.2 生成自签名SSL证书（测试用）
```bash
# 生成私钥
sudo openssl genrsa -out /etc/nginx/ssl/nginx.key 2048

# 生成证书签名请求
sudo openssl req -new -key /etc/nginx/ssl/nginx.key -out /etc/nginx/ssl/nginx.csr

# 生成自签名证书（有效期365天）
sudo openssl x509 -req -days 365 -in /etc/nginx/ssl/nginx.csr -signkey /etc/nginx/ssl/nginx.key -out /etc/nginx/ssl/nginx.crt

# 设置证书文件权限
sudo chmod 600 /etc/nginx/ssl/nginx.key
sudo chmod 644 /etc/nginx/ssl/nginx.crt
```

### 5.3 配置HTTPS站点
```bash
# 创建HTTPS站点配置
sudo tee /etc/nginx/conf.d/https.conf << 'EOF'
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    root /var/www/html;
    index index.html index.htm;

    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/nginx.crt;
    ssl_certificate_key /etc/nginx/ssl/nginx.key;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # HSTS安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    location / {
        try_files $uri $uri/ =404;
    }

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# HTTP到HTTPS重定向
server {
    listen 80;
    listen [::]:80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}
EOF
```

---

## 6. 反向代理配置

### 6.1 基础反向代理配置
```bash
# 创建反向代理配置文件
sudo tee /etc/nginx/conf.d/reverse-proxy.conf << 'EOF'
# 上游服务器定义
upstream backend_servers {
    server 127.0.0.1:8080;  # 后端应用服务器
    # server 127.0.0.1:8081;  # 可以添加更多后端服务器
}

server {
    listen 80;
    server_name api.your-domain.com;

    # 反向代理配置
    location / {
        proxy_pass http://backend_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 代理超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 缓冲区设置
        proxy_buffer_size 4k;
        proxy_buffers 4 32k;
        proxy_busy_buffers_size 64k;
    }

    # API接口代理
    location /api/ {
        proxy_pass http://backend_servers/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # API特殊配置
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket代理支持
    location /ws/ {
        proxy_pass http://backend_servers;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF
```

### 6.2 Spring Boot应用代理配置
```bash
# 创建Spring Boot应用代理配置
sudo tee /etc/nginx/conf.d/springboot-proxy.conf << 'EOF'
upstream springboot_app {
    server 127.0.0.1:8080 weight=1 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

server {
    listen 80;
    server_name app.your-domain.com;

    # 访问日志
    access_log /var/log/nginx/springboot_access.log main;
    error_log /var/log/nginx/springboot_error.log;

    # 静态资源直接服务
    location /static/ {
        alias /var/www/static/;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    # Spring Boot应用代理
    location / {
        proxy_pass http://springboot_app;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # 超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 8k;
        proxy_buffers 8 8k;
    }

    # 健康检查端点
    location /actuator/health {
        proxy_pass http://springboot_app/actuator/health;
        access_log off;
    }
}
EOF
```

---

## 7. 负载均衡配置

### 7.1 基础负载均衡配置
```bash
# 创建负载均衡配置文件
sudo tee /etc/nginx/conf.d/load-balancer.conf << 'EOF'
# 定义后端服务器组
upstream web_servers {
    # 轮询（默认）
    server ************:8080 weight=3 max_fails=3 fail_timeout=30s;
    server ************:8080 weight=2 max_fails=3 fail_timeout=30s;
    server ************:8080 weight=1 max_fails=3 fail_timeout=30s backup;

    # 保持连接
    keepalive 32;
}

# 数据库服务器组（只读）
upstream db_read_servers {
    server ************:3306 weight=2;
    server ************:3306 weight=1;
    least_conn;  # 最少连接数算法
}

server {
    listen 80;
    server_name lb.your-domain.com;

    # 负载均衡配置
    location / {
        proxy_pass http://web_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 连接保持
        proxy_http_version 1.1;
        proxy_set_header Connection "";

        # 健康检查
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 10s;
    }

    # API负载均衡
    location /api/ {
        proxy_pass http://web_servers/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        # 会话保持（基于IP）
        ip_hash;
    }

    # 状态监控页面
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        allow ***********/24;
        deny all;
    }
}
EOF
```

### 7.2 高级负载均衡配置
```bash
# 创建高级负载均衡配置
sudo tee /etc/nginx/conf.d/advanced-lb.conf << 'EOF'
# 定义多个服务器组
upstream app_servers {
    # 一致性哈希（需要安装hash模块）
    hash $request_uri consistent;
    server ************:8080 weight=3;
    server ************:8080 weight=2;
    server ************:8080 weight=1;
}

upstream static_servers {
    server ************:80;
    server ************:80;
    least_conn;
}

# 地理位置映射
geo $geo {
    default 0;
    127.0.0.1 1;
    ***********/24 1;
}

# 根据地理位置选择上游
map $geo $pool {
    0 app_servers;
    1 app_servers;
}

server {
    listen 80;
    server_name advanced-lb.your-domain.com;

    # 动态内容负载均衡
    location / {
        proxy_pass http://$pool;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 静态内容负载均衡
    location /static/ {
        proxy_pass http://static_servers;
        proxy_cache_valid 200 1h;
        expires 1h;
    }

    # 基于URI的路由
    location /app1/ {
        proxy_pass http://************:8080/;
    }

    location /app2/ {
        proxy_pass http://************:8080/;
    }
}
EOF
```

---

## 8. 性能优化配置

### 8.1 ARM64架构性能优化
```bash
# 创建ARM64性能优化配置
sudo tee /etc/nginx/conf.d/performance.conf << 'EOF'
# ARM64性能优化配置文件

# 工作进程优化
worker_processes auto;  # 自动检测CPU核心数
worker_cpu_affinity auto;  # CPU亲和性
worker_rlimit_nofile 65535;  # 文件描述符限制

events {
    worker_connections 8192;  # ARM64优化连接数
    use epoll;  # Linux下最高效的事件模型
    multi_accept on;  # 一次接受多个连接
    accept_mutex off;  # 关闭accept锁
}

http {
    # 连接优化
    keepalive_timeout 65;
    keepalive_requests 1000;
    send_timeout 60;

    # 缓冲区优化
    client_body_buffer_size 128k;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;
    client_max_body_size 100m;

    # 输出缓冲优化
    output_buffers 2 32k;
    postpone_output 1460;

    # TCP优化
    tcp_nopush on;
    tcp_nodelay on;

    # 文件传输优化
    sendfile on;
    sendfile_max_chunk 512k;

    # 压缩优化
    gzip on;
    gzip_vary on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        font/opentype;

    # 缓存优化
    open_file_cache max=65535 inactive=60s;
    open_file_cache_valid 80s;
    open_file_cache_min_uses 1;
    open_file_cache_errors on;
}
EOF
```

### 8.2 系统级性能优化
```bash
# 优化系统参数
sudo tee -a /etc/sysctl.conf << 'EOF'
# Nginx性能优化参数
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 6000
net.ipv4.ip_local_port_range = 1024 65535
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_tw_recycle = 0
net.ipv4.tcp_timestamps = 1
fs.file-max = 65535
EOF

# 应用系统参数
sudo sysctl -p

# 优化文件描述符限制
sudo tee -a /etc/security/limits.conf << 'EOF'
nginx soft nofile 65535
nginx hard nofile 65535
* soft nofile 65535
* hard nofile 65535
EOF
```

### 8.3 日志优化配置
```bash
# 创建日志优化配置
sudo tee /etc/logrotate.d/nginx << 'EOF'
/var/log/nginx/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 640 nginx nginx
    sharedscripts
    postrotate
        if [ -f /var/run/nginx.pid ]; then
            kill -USR1 `cat /var/run/nginx.pid`
        fi
    endscript
}
EOF

# 测试日志轮转配置
sudo logrotate -d /etc/logrotate.d/nginx
```

---

## 9. 安全配置

### 9.1 基础安全配置
```bash
# 创建安全配置文件
sudo tee /etc/nginx/conf.d/security.conf << 'EOF'
# Nginx安全配置

# 隐藏版本信息
server_tokens off;

# 安全头配置
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;" always;

# 限制请求方法
map $request_method $allowed_method {
    default 1;
    GET 0;
    POST 0;
    HEAD 0;
    PUT 0;
    DELETE 0;
}

# 限制文件上传大小
client_max_body_size 10M;

# 超时设置
client_body_timeout 12;
client_header_timeout 12;
send_timeout 10;

# 缓冲区限制
client_body_buffer_size 16K;
client_header_buffer_size 1k;
large_client_header_buffers 2 1k;

server {
    listen 80;
    server_name secure.your-domain.com;

    # 拒绝不允许的请求方法
    if ($allowed_method) {
        return 405;
    }

    # 拒绝访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 拒绝访问备份文件
    location ~* \.(bak|backup|old|orig|original|tmp)$ {
        deny all;
    }

    # 限制特定文件类型访问
    location ~* \.(sql|log|conf)$ {
        deny all;
    }

    # 防止目录遍历
    location / {
        try_files $uri $uri/ =404;
    }
}
EOF
```

### 9.2 访问控制和限流
```bash
# 创建访问控制配置
sudo tee /etc/nginx/conf.d/access-control.conf << 'EOF'
# 定义限流区域
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=general:10m rate=1r/s;

# 连接数限制
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

server {
    listen 80;
    server_name protected.your-domain.com;

    # 连接数限制
    limit_conn conn_limit_per_ip 10;

    # IP白名单
    location /admin/ {
        allow ***********/24;
        allow 127.0.0.1;
        deny all;

        # 管理员区域限流
        limit_req zone=login burst=3 nodelay;
    }

    # API限流
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        limit_req_status 429;
    }

    # 一般页面限流
    location / {
        limit_req zone=general burst=5 nodelay;
    }

    # 错误页面
    error_page 429 /429.html;
    location = /429.html {
        root /var/www/html;
        internal;
    }
}
EOF

# 创建429错误页面
sudo tee /var/www/html/429.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>请求过于频繁</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>429 - 请求过于频繁</h1>
    <p>您的请求过于频繁，请稍后再试。</p>
</body>
</html>
EOF
```

---

## 10. 验证安装和功能测试

### 10.1 基础功能验证
```bash
# 检查Nginx版本和编译信息
nginx -v
nginx -V

# 检查配置文件语法
sudo nginx -t

# 检查服务状态
sudo systemctl status nginx

# 检查端口监听
sudo netstat -tlnp | grep nginx
sudo ss -tlnp | grep nginx

# 检查进程信息
ps aux | grep nginx
```

### 10.2 HTTP功能测试
```bash
# 测试HTTP访问
curl -I http://localhost
curl -I http://localhost/index.html

# 测试响应头
curl -I http://localhost | grep -E "(Server|X-Frame-Options|X-XSS-Protection)"

# 测试Gzip压缩
curl -H "Accept-Encoding: gzip" -I http://localhost

# 测试静态文件服务
echo "Test static file" | sudo tee /var/www/html/test.txt
curl http://localhost/test.txt
```

### 10.3 HTTPS功能测试
```bash
# 测试HTTPS访问（如果配置了SSL）
curl -k -I https://localhost

# 测试SSL证书信息
openssl s_client -connect localhost:443 -servername localhost < /dev/null

# 测试HTTP到HTTPS重定向
curl -I http://localhost
```

### 10.4 负载均衡测试
```bash
# 创建测试脚本
sudo tee /tmp/test_lb.sh << 'EOF'
#!/bin/bash
for i in {1..10}; do
    echo "Request $i:"
    curl -s http://localhost | grep -o "Server: [^<]*" || echo "No server info"
    sleep 1
done
EOF

chmod +x /tmp/test_lb.sh
/tmp/test_lb.sh
```

### 10.5 性能测试
```bash
# 安装Apache Bench（如果没有）
sudo yum install -y httpd-tools

# 基础性能测试
ab -n 1000 -c 10 http://localhost/

# 并发测试
ab -n 5000 -c 100 http://localhost/

# 长时间测试
ab -t 60 -c 50 http://localhost/
```

---

## 11. ARM架构和麒麟系统特殊注意事项

### 11.1 ARM64架构优化配置

**⚠️ ARM64特殊配置要点：**

```bash
# ARM64架构特殊优化参数
sudo tee -a /etc/nginx/nginx.conf << 'EOF'
# ARM64架构优化
worker_cpu_affinity auto;
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 4096;  # ARM64推荐值
    use epoll;
    multi_accept on;
}

http {
    # ARM64内存优化
    client_body_buffer_size 128k;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;

    # ARM64 TCP优化
    tcp_nopush on;
    tcp_nodelay on;
    sendfile on;
    sendfile_max_chunk 512k;
}
EOF
```

### 11.2 麒麟系统兼容性配置

```bash
# 检查麒麟系统版本
cat /etc/kylin-release

# 麒麟系统特殊的SELinux配置
sudo setsebool -P httpd_can_network_connect 1
sudo setsebool -P httpd_can_network_relay 1
sudo setsebool -P httpd_execmem 1

# 麒麟系统防火墙配置
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload

# 检查系统资源限制
ulimit -n
cat /proc/sys/fs/file-max
```

### 11.3 ARM64性能调优建议

```bash
# CPU亲和性设置
sudo tee /etc/nginx/conf.d/arm64-tuning.conf << 'EOF'
# ARM64 CPU优化
worker_processes auto;
worker_cpu_affinity auto;

# ARM64内存管理
worker_rlimit_nofile 65535;
worker_rlimit_core 50M;

events {
    worker_connections 8192;
    worker_aio_requests 32;
}

http {
    # ARM64缓存优化
    open_file_cache max=65535 inactive=60s;
    open_file_cache_valid 80s;
    open_file_cache_min_uses 1;

    # ARM64网络优化
    keepalive_timeout 65;
    keepalive_requests 1000;

    # ARM64压缩优化
    gzip_comp_level 6;  # ARM64推荐压缩级别
    gzip_buffers 16 8k;
}
EOF
```

### 11.4 编译优化（如需从源码安装）

```bash
# ARM64编译优化参数
export CFLAGS="-O2 -march=armv8-a -mtune=cortex-a72"
export CXXFLAGS="-O2 -march=armv8-a -mtune=cortex-a72"

# 配置编译选项
./configure \
    --prefix=/etc/nginx \
    --sbin-path=/usr/sbin/nginx \
    --modules-path=/usr/lib64/nginx/modules \
    --conf-path=/etc/nginx/nginx.conf \
    --error-log-path=/var/log/nginx/error.log \
    --http-log-path=/var/log/nginx/access.log \
    --pid-path=/var/run/nginx.pid \
    --lock-path=/var/run/nginx.lock \
    --with-http_ssl_module \
    --with-http_realip_module \
    --with-http_addition_module \
    --with-http_sub_module \
    --with-http_dav_module \
    --with-http_flv_module \
    --with-http_mp4_module \
    --with-http_gunzip_module \
    --with-http_gzip_static_module \
    --with-http_random_index_module \
    --with-http_secure_link_module \
    --with-http_stub_status_module \
    --with-http_auth_request_module \
    --with-http_xslt_module=dynamic \
    --with-http_image_filter_module=dynamic \
    --with-http_geoip_module=dynamic \
    --with-threads \
    --with-stream \
    --with-stream_ssl_module \
    --with-stream_ssl_preread_module \
    --with-stream_realip_module \
    --with-stream_geoip_module=dynamic \
    --with-http_slice_module \
    --with-mail \
    --with-mail_ssl_module \
    --with-compat \
    --with-file-aio \
    --with-http_v2_module
```

---

## 12. 常见问题排查

### 12.1 安装问题

**问题1：YUM安装失败**

```bash
# 检查网络连接
ping -c 3 mirrors.aliyun.com

# 清理YUM缓存
sudo yum clean all
sudo yum makecache

# 检查EPEL源
sudo yum repolist | grep epel

# 重新安装EPEL源
sudo yum install -y epel-release
```

**问题2：依赖包冲突**

```bash
# 查看冲突的包
sudo yum deplist nginx

# 强制安装
sudo yum install -y nginx --skip-broken

# 或者使用DNF（如果可用）
sudo dnf install -y nginx
```

### 12.2 服务启动问题

**问题1：服务启动失败**

```bash
# 查看详细错误信息
sudo systemctl status nginx -l
sudo journalctl -u nginx -f

# 检查配置文件语法
sudo nginx -t

# 检查端口占用
sudo netstat -tlnp | grep :80
sudo lsof -i :80
```

**问题2：权限问题**

```bash
# 检查nginx用户
id nginx

# 检查文件权限
ls -la /etc/nginx/
ls -la /var/log/nginx/
ls -la /var/www/html/

# 修复权限
sudo chown -R nginx:nginx /var/log/nginx
sudo chown -R nginx:nginx /var/www/html
sudo chmod 755 /var/www/html
```

### 12.3 性能问题

**问题1：连接数过多**

```bash
# 检查当前连接数
sudo netstat -an | grep :80 | wc -l

# 检查worker进程数
ps aux | grep nginx

# 优化worker配置
sudo vim /etc/nginx/nginx.conf
# 调整 worker_processes 和 worker_connections
```

**问题2：内存使用过高**

```bash
# 检查内存使用
free -h
ps aux --sort=-%mem | head

# 优化缓冲区设置
sudo vim /etc/nginx/nginx.conf
# 调整各种buffer大小
```

### 12.4 SSL/HTTPS问题

**问题1：SSL证书错误**

```bash
# 检查证书文件
sudo ls -la /etc/nginx/ssl/

# 验证证书
sudo openssl x509 -in /etc/nginx/ssl/nginx.crt -text -noout

# 检查私钥
sudo openssl rsa -in /etc/nginx/ssl/nginx.key -check
```

**问题2：HTTPS重定向循环**

```bash
# 检查配置文件中的重定向规则
sudo grep -r "return 301" /etc/nginx/

# 检查代理配置
sudo grep -r "proxy_set_header.*X-Forwarded-Proto" /etc/nginx/
```

### 12.5 ARM64特殊问题

**问题1：性能不佳**

```bash
# 检查CPU架构优化
grep -r "worker_cpu_affinity" /etc/nginx/

# 检查编译参数
nginx -V | grep -o "configure arguments.*"

# 优化系统参数
sudo sysctl -a | grep net.core
```

**问题2：模块加载失败**

```bash
# 检查可用模块
nginx -V 2>&1 | grep -o "with-[a-z_]*"

# 检查动态模块
ls -la /usr/lib64/nginx/modules/

# 加载模块
echo "load_module modules/ngx_http_image_filter_module.so;" | sudo tee -a /etc/nginx/nginx.conf
```

### 12.6 日志分析

**重要日志文件位置：**

```bash
# Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# Nginx访问日志
sudo tail -f /var/log/nginx/access.log

# 系统日志
sudo tail -f /var/log/messages

# 服务日志
sudo journalctl -u nginx -f
```

**日志分析命令：**

```bash
# 分析访问日志
sudo awk '{print $1}' /var/log/nginx/access.log | sort | uniq -c | sort -nr | head -10

# 分析错误日志
sudo grep "error" /var/log/nginx/error.log | tail -20

# 分析状态码
sudo awk '{print $9}' /var/log/nginx/access.log | sort | uniq -c | sort -nr
```

---

## 总结

本文档详细介绍了在ARM64银河麒麟V10 SP3系统上安装和配置Nginx的完整流程，包括：

✅ **完成的配置项：**
- 系统环境检查和准备工作
- 使用YUM包管理器安装Nginx
- 基础配置文件设置和优化
- 服务启动和开机自启动配置
- SSL/HTTPS配置示例
- 反向代理和负载均衡配置
- ARM64架构性能优化
- 安全配置和访问控制
- 完整的验证和测试流程

✅ **关键特性：**
- 针对ARM64架构优化
- 银河麒麟系统兼容性配置
- 高性能负载均衡
- 完整的安全防护
- 详细的故障排查指南

**🔧 后续维护建议：**
- 定期更新Nginx版本
- 监控系统性能指标
- 定期检查安全配置
- 备份重要配置文件
- 关注安全补丁更新

通过本文档的配置，您将获得一个高性能、安全可靠的Nginx Web服务器，能够满足生产环境的各种需求。
