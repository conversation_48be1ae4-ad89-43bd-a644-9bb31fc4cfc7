<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-15T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17">
  <diagram name="System Architecture" id="system-arch">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Load Balancer Layer -->
        <mxCell id="nginx-lb" value="Nginx Load Balancer&#xa;(ARM64 Kylin Linux)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="480" y="40" width="200" height="60" as="geometry" />
        </mxCell>
        
        <!-- Web Server Layer -->
        <mxCell id="nginx1" value="Nginx Web Server 1&#xa;(Static Files + Reverse Proxy)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="200" y="160" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="nginx2" value="Nginx Web Server 2&#xa;(Static Files + Reverse Proxy)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="600" y="160" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- Application Server Layer -->
        <mxCell id="tomcat1" value="Tomcat Server 1&#xa;(Java 8 + Spring Boot)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="120" y="280" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="tomcat2" value="Tomcat Server 2&#xa;(Java 8 + Spring Boot)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="320" y="280" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="tomcat3" value="Tomcat Server 3&#xa;(Java 8 + Spring Boot)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="520" y="280" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- Message Queue -->
        <mxCell id="activemq" value="ActiveMQ&#xa;(Message Broker)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="720" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Cache Layer -->
        <mxCell id="redis" value="Redis Cluster&#xa;(Cache + Session)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="40" y="400" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- Database Layer -->
        <mxCell id="mysql-master" value="MySQL Master&#xa;(Read/Write)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="280" y="400" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="mysql-slave1" value="MySQL Slave 1&#xa;(Read Only)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="440" y="400" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="mysql-slave2" value="MySQL Slave 2&#xa;(Read Only)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="600" y="400" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Connections -->
        <mxCell id="conn1" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="nginx-lb" target="nginx1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn2" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="nginx-lb" target="nginx2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn3" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="nginx1" target="tomcat1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn4" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="nginx1" target="tomcat2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn5" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="nginx2" target="tomcat2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn6" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="nginx2" target="tomcat3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Database connections -->
        <mxCell id="db-conn1" value="Write" style="endArrow=classic;html=1;rounded=0;strokeColor=#d79b00;" edge="1" parent="1" source="tomcat1" target="mysql-master">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="350" as="sourcePoint" />
            <mxPoint x="450" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="db-conn2" value="Read" style="endArrow=classic;html=1;rounded=0;strokeColor=#6c8ebf;" edge="1" parent="1" source="tomcat2" target="mysql-slave1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="350" as="sourcePoint" />
            <mxPoint x="450" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="db-conn3" value="Read" style="endArrow=classic;html=1;rounded=0;strokeColor=#6c8ebf;" edge="1" parent="1" source="tomcat3" target="mysql-slave2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="350" as="sourcePoint" />
            <mxPoint x="450" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Replication -->
        <mxCell id="repl1" value="Replication" style="endArrow=classic;html=1;rounded=0;strokeColor=#82b366;" edge="1" parent="1" source="mysql-master" target="mysql-slave1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="repl2" value="Replication" style="endArrow=classic;html=1;rounded=0;strokeColor=#82b366;" edge="1" parent="1" source="mysql-master" target="mysql-slave2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Cache connections -->
        <mxCell id="cache-conn1" value="Cache" style="endArrow=classic;html=1;rounded=0;strokeColor=#6c8ebf;" edge="1" parent="1" source="tomcat1" target="redis">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="350" as="sourcePoint" />
            <mxPoint x="450" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- MQ connections -->
        <mxCell id="mq-conn1" value="Message" style="endArrow=classic;html=1;rounded=0;strokeColor=#b85450;" edge="1" parent="1" source="tomcat3" target="activemq">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="350" as="sourcePoint" />
            <mxPoint x="450" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Title -->
        <mxCell id="title" value="ARM64 Kylin Linux 系统架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="480" y="10" width="200" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
