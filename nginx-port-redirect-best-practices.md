# Nginx端口重定向最佳实践

## 问题分析

在端口映射环境中，HTTP到HTTPS重定向需要特别处理，因为：
- 外部端口和内部端口不同
- 需要重定向到正确的外部HTTPS端口
- 不应该硬编码域名和端口

## 最佳实践方案

### 方案1：使用变量（推荐）

```nginx
# HTTP服务器配置
server {
    listen 80;
    listen [::]:80;
    server_name example.com;
    
    # 设置HTTPS端口变量（便于维护）
    set $https_port 8081;
    
    # 重定向到HTTPS
    return 301 https://$server_name:$https_port$request_uri;
}
```

**优点：**
- 易于维护，只需修改一个变量
- 支持多域名
- 配置清晰

### 方案2：使用map指令（多域名环境）

```nginx
# 在http块中定义端口映射
map $server_name $https_port {
    default 443;
    example.com 8081;
    test.example.com 8082;
    api.example.com 8083;
}

server {
    listen 80;
    server_name example.com test.example.com api.example.com;
    
    return 301 https://$server_name:$https_port$request_uri;
}
```

**优点：**
- 支持多域名不同端口
- 集中管理端口配置
- 高度灵活

### 方案3：条件重定向

```nginx
server {
    listen 80;
    server_name example.com;
    
    # 根据不同路径重定向到不同端口
    location /app1/ {
        return 301 https://$server_name:8081$request_uri;
    }
    
    location /app2/ {
        return 301 https://$server_name:8082$request_uri;
    }
    
    # 默认重定向
    location / {
        return 301 https://$server_name:8081$request_uri;
    }
}
```

### 方案4：使用include文件

创建 `/etc/nginx/conf.d/port-config.conf`：
```nginx
# 端口配置文件
set $https_port 8081;
set $api_port 8082;
```

在主配置中：
```nginx
server {
    listen 80;
    server_name example.com;
    
    # 包含端口配置
    include /etc/nginx/conf.d/port-config.conf;
    
    return 301 https://$server_name:$https_port$request_uri;
}
```

## 环境变量方案（Docker环境）

```nginx
# 使用环境变量
server {
    listen 80;
    server_name ${DOMAIN_NAME};
    
    return 301 https://${DOMAIN_NAME}:${HTTPS_PORT}$request_uri;
}
```

配合Docker：
```dockerfile
ENV DOMAIN_NAME=example.com
ENV HTTPS_PORT=8081
```

## 为什么不建议硬编码？

### 硬编码的问题：
```nginx
# 不推荐
return 301 https://example.com:8081$request_uri;
```

**缺点：**
1. **维护困难**：域名或端口变更需要修改多处
2. **不够灵活**：无法适应不同环境（开发/测试/生产）
3. **多域名支持差**：每个域名需要单独配置
4. **配置重复**：相同逻辑在多处重复

### 变量方案的优势：
```nginx
# 推荐
set $https_port 8081;
return 301 https://$server_name:$https_port$request_uri;
```

**优点：**
1. **易于维护**：只需修改变量值
2. **支持多域名**：$server_name自动适配
3. **环境友好**：不同环境可以使用不同配置
4. **配置复用**：逻辑可以在多处使用

## 实际应用示例

### 开发环境配置：
```nginx
server {
    listen 80;
    server_name localhost dev.example.com;
    
    set $https_port 8081;
    return 301 https://$server_name:$https_port$request_uri;
}
```

### 生产环境配置：
```nginx
server {
    listen 80;
    server_name example.com www.example.com;
    
    set $https_port 443;  # 生产环境使用标准端口
    return 301 https://$server_name:$https_port$request_uri;
}
```

### 测试环境配置：
```nginx
server {
    listen 80;
    server_name test.example.com;
    
    set $https_port 8443;  # 测试环境使用不同端口
    return 301 https://$server_name:$https_port$request_uri;
}
```

## 配置验证

```bash
# 检查配置语法
nginx -t

# 测试重定向
curl -I http://example.com:8082/

# 预期输出
HTTP/1.1 301 Moved Permanently
Location: https://example.com:8081/
```

## 总结

**最佳实践建议：**
1. **使用变量**而不是硬编码
2. **集中管理**端口配置
3. **考虑多环境**部署需求
4. **保持配置**的可读性和可维护性

**当前您的配置已经改为：**
```nginx
set $https_port 8081;
return 301 https://$server_name:$https_port$request_uri;
```

这样既解决了重定向问题，又保持了配置的灵活性！
