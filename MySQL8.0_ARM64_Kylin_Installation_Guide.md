# MySQL 8.0 在 ARM64 银河麒麟 V10 SP3 系统安装教程

## 目录
1. [系统环境检查和准备工作](#1-系统环境检查和准备工作)
2. [MySQL 8.0 下载和安装](#2-mysql-80-下载和安装)
3. [配置文件设置和优化](#3-配置文件设置和优化)
4. [服务启动和开机自启动](#4-服务启动和开机自启动)
5. [安全配置](#5-安全配置)
6. [ARM架构和麒麟系统特殊注意事项](#6-arm架构和麒麟系统特殊注意事项)
7. [验证步骤](#7-验证步骤)
8. [常见问题排查](#8-常见问题排查)

---

## 1. 系统环境检查和准备工作

### 1.1 检查系统信息
```bash
# 检查操作系统版本
cat /etc/os-release

# 检查处理器架构
uname -m

# 检查内核版本
uname -r

# 检查可用内存
free -h

# 检查磁盘空间
df -h
```

**预期输出示例：**
- 架构应显示：`aarch64`
- 操作系统：`<PERSON><PERSON><PERSON> Linux V10 SP3`

### 1.2 更新系统包
```bash
# 更新包管理器缓存
sudo yum update -y

# 安装必要的开发工具
sudo yum groupinstall -y "Development Tools"
sudo yum install -y wget curl vim net-tools
```

### 1.3 检查端口占用
```bash
# 检查MySQL默认端口3306是否被占用
netstat -tlnp | grep :3306

# 如果端口被占用，查看占用进程
sudo lsof -i :3306
```

### 1.4 创建MySQL用户和组
```bash
# 创建mysql用户组
sudo groupadd mysql

# 创建mysql用户
sudo useradd -r -g mysql -s /bin/false mysql
```

---

## 2. MySQL 8.0 下载和安装

### 2.0 中国大陆用户镜像源配置（重要）

**🇨🇳 强烈建议中国大陆用户使用国内镜像源！**

MySQL官方源在中国大陆访问速度很慢，经常出现超时或下载失败。以下是推荐的国内镜像源：

#### 2.0.1 阿里云镜像源（首选推荐）
```bash
# 优势：速度快、稳定性好、更新及时
# 适用：所有中国大陆用户
```

#### 2.0.2 其他可选镜像源
```bash
# 清华大学镜像源 - 教育网用户友好
# 华为云镜像源 - 企业级稳定性
# 网易镜像源 - 备选方案
```

#### 2.0.3 网络环境检测
```bash
# 测试网络连通性
ping -c 3 mirrors.aliyun.com
ping -c 3 repo.mysql.com

# 测试下载速度对比
time wget -O /dev/null --timeout=10 https://mirrors.aliyun.com/mysql/yum/mysql80-community-release-el8-1.noarch.rpm
time wget -O /dev/null --timeout=10 https://dev.mysql.com/get/mysql80-community-release-el8-1.noarch.rpm
```

**💡 建议：如果阿里云镜像源测试速度明显更快，请使用下面的阿里云安装方法。**

### 2.1 方法一：使用YUM源安装（推荐）

#### 2.1.1 添加MySQL YUM源

**🌍 推荐方案：使用官方源（经验证稳定可靠）**
```bash
# 实际验证的安装步骤（推荐）
# 1. 下载官方MySQL YUM源配置包
wget https://dev.mysql.com/get/mysql80-community-release-el8-1.noarch.rpm

# 2. 安装YUM源配置
sudo rpm -Uvh mysql80-community-release-el8-1.noarch.rpm

# 3. 清理缓存并重新生成
sudo yum clean all
sudo yum makecache

# 4. 验证源是否添加成功
yum repolist enabled | grep mysql
```

**🇨🇳 中国大陆用户可选择镜像源（如果官方源速度慢）：**
```bash
# 注意：阿里云镜像可能存在路径问题，建议优先使用官方源
# 1. 下载官方MySQL YUM源配置包
wget https://dev.mysql.com/get/mysql80-community-release-el8-1.noarch.rpm

# 2. 安装YUM源配置
sudo rpm -Uvh mysql80-community-release-el8-1.noarch.rpm

# 3. 修改为阿里云镜像源（可选，可能遇到404错误）
sudo sed -i 's|repo.mysql.com|mirrors.aliyun.com/mysql|g' /etc/yum.repos.d/mysql-community.repo

# 4. 清理缓存并重新生成
sudo yum clean all
sudo yum makecache

# 5. 验证源是否添加成功
yum repolist enabled | grep mysql
```

**🔧 方法二：手动创建阿里云镜像源配置（备选方案）：**
```bash
# 如果上述方法不工作，可以手动创建配置文件
sudo tee /etc/yum.repos.d/mysql-community.repo > /dev/null <<EOF
[mysql80-community]
name=MySQL 8.0 Community Server
baseurl=https://mirrors.aliyun.com/mysql/yum/mysql-8.0-community/el/8/\$basearch/
enabled=1
gpgcheck=1
gpgkey=https://repo.mysql.com/RPM-GPG-KEY-mysql-2022

[mysql-tools-community]
name=MySQL Tools Community
baseurl=https://mirrors.aliyun.com/mysql/yum/mysql-tools-community/el/8/\$basearch/
enabled=1
gpgcheck=1
gpgkey=https://repo.mysql.com/RPM-GPG-KEY-mysql-2022
EOF

# 清理缓存并验证
sudo yum clean all
sudo yum makecache
yum repolist enabled | grep mysql
```

**🧪 验证镜像源可用性：**
```bash
# 测试阿里云镜像连通性
curl -I https://mirrors.aliyun.com/mysql/yum/mysql-8.0-community/el/8/aarch64/

# 查看可用的MySQL包
yum list available | grep mysql-community-server
```

**🌍 海外用户或有稳定国际网络的用户可使用官方源：**
```bash
# 下载MySQL官方YUM源配置包
wget https://dev.mysql.com/get/mysql80-community-release-el8-1.noarch.rpm

# 安装YUM源配置
sudo rpm -Uvh mysql80-community-release-el8-1.noarch.rpm

# 验证源是否添加成功
yum repolist enabled | grep mysql
```

**⚠️ 重要注意事项：**
- **404错误解决**：如果直接下载阿里云配置包遇到404错误，请使用方法一（先下载官方包再修改）
- **网络问题**：如果wget下载失败，可能是网络问题，建议使用代理或尝试其他镜像源
- **速度优势**：阿里云镜像源在中国大陆访问速度通常比官方源快5-10倍
- **验证重要性**：安装前务必验证镜像源可用性，避免后续安装失败

#### 2.1.2 安装MySQL 8.0

**⚠️ 重要：检查系统中是否已安装MariaDB**
```bash
# 检查是否安装了MariaDB
rpm -qa | grep mariadb
systemctl status mariadb
```

**如果系统中已安装MariaDB，需要先卸载：**
```bash
# 1. 停止MariaDB服务
sudo systemctl stop mariadb

# 2. 卸载MariaDB相关包
sudo yum remove -y mariadb-server mariadb mariadb-common mariadb-libs mariadb-connector-c

# 3. 清理配置文件冲突
sudo rm -f /etc/my.cnf

# 4. 清理YUM缓存
sudo yum clean all
```

**安装MySQL：**
```bash
# 安装MySQL服务器
sudo yum install -y mysql-community-server

# 如果遇到GPG密钥问题，使用以下命令：
sudo yum install -y mysql-community-server --nogpgcheck

# 验证安装成功
rpm -qa | grep mysql-community
```

**常见问题解决：**
```bash
# 问题1：文件冲突错误
# 错误信息：file /etc/my.cnf from install of mysql-community-server conflicts with file from package mariadb-common
# 解决方案：按照上述步骤卸载MariaDB

# 问题2：GPG密钥验证失败
# 解决方案：使用 --nogpgcheck 参数跳过GPG检查
sudo yum install -y mysql-community-server --nogpgcheck
```

### 2.2 方法二：下载二进制包安装

#### 2.2.1 下载ARM64版本的MySQL二进制包
```bash
# 创建下载目录
mkdir -p ~/mysql-install && cd ~/mysql-install
```

**🇨🇳 中国大陆用户推荐使用阿里云镜像下载：**
```bash
# 从阿里云镜像下载（速度更快）
wget https://mirrors.aliyun.com/mysql/Downloads/MySQL-8.0/mysql-8.0.35-linux-glibc2.28-aarch64.tar.xz

# 验证下载文件
ls -la mysql-8.0.35-linux-glibc2.28-aarch64.tar.xz
```

**🌍 海外用户或网络良好的用户可使用官方下载：**
```bash
# 从MySQL官方下载
wget https://dev.mysql.com/get/Downloads/MySQL-8.0/mysql-8.0.35-linux-glibc2.28-aarch64.tar.xz

# 验证下载文件
ls -la mysql-8.0.35-linux-glibc2.28-aarch64.tar.xz
```

**💡 下载速度对比测试：**
```bash
# 测试阿里云镜像下载速度
time wget -O /dev/null --timeout=30 https://mirrors.aliyun.com/mysql/Downloads/MySQL-8.0/mysql-8.0.35-linux-glibc2.28-aarch64.tar.xz

# 测试官方源下载速度
time wget -O /dev/null --timeout=30 https://dev.mysql.com/get/Downloads/MySQL-8.0/mysql-8.0.35-linux-glibc2.28-aarch64.tar.xz
```

#### 2.2.2 解压和安装
```bash
# 解压二进制包
tar -xf mysql-8.0.35-linux-glibc2.28-aarch64.tar.xz

# 移动到安装目录
sudo mv mysql-8.0.35-linux-glibc2.28-aarch64 /usr/local/mysql

# 设置目录权限
sudo chown -R mysql:mysql /usr/local/mysql
```

### 2.3 方法三：编译安装（高级用户）

#### 2.3.1 安装编译依赖
```bash
# 安装编译所需的依赖包
sudo yum install -y cmake gcc gcc-c++ ncurses-devel bison openssl-devel
sudo yum install -y libtirpc-devel rpcgen
```

#### 2.3.2 下载源码并编译

**🇨🇳 中国大陆用户推荐使用阿里云镜像下载源码：**
```bash
# 从阿里云镜像下载MySQL源码
wget https://mirrors.aliyun.com/mysql/Downloads/MySQL-8.0/mysql-8.0.35.tar.gz

# 解压源码
tar -xzf mysql-8.0.35.tar.gz
cd mysql-8.0.35
```

**🌍 海外用户可使用官方源码：**
```bash
# 从MySQL官方下载源码
wget https://dev.mysql.com/get/Downloads/MySQL-8.0/mysql-8.0.35.tar.gz

# 解压源码
tar -xzf mysql-8.0.35.tar.gz
cd mysql-8.0.35
```

**编译配置和构建：**
```bash
# 创建构建目录
mkdir build && cd build

# 配置编译选项
cmake .. \
  -DCMAKE_INSTALL_PREFIX=/usr/local/mysql \
  -DMYSQL_DATADIR=/usr/local/mysql/data \
  -DSYSCONFDIR=/etc \
  -DMYSQL_TCP_PORT=3306 \
  -DMYSQL_UNIX_ADDR=/tmp/mysql.sock \
  -DDEFAULT_CHARSET=utf8mb4 \
  -DDEFAULT_COLLATION=utf8mb4_unicode_ci \
  -DWITH_INNOBASE_STORAGE_ENGINE=1 \
  -DWITH_ARCHIVE_STORAGE_ENGINE=1 \
  -DWITH_BLACKHOLE_STORAGE_ENGINE=1 \
  -DWITH_PERFSCHEMA_STORAGE_ENGINE=1 \
  -DMYSQL_USER=mysql \
  -DWITH_DEBUG=0 \
  -DWITH_SSL=system

# 编译（这个过程可能需要1-2小时）
make -j$(nproc)

# 安装
sudo make install
```

---

## 3. 配置文件设置和优化

### 3.1 创建必要的目录

**⚠️ 注意：如果您已经运行了 `mysqld --initialize`，数据目录可能已经创建。**

```bash
# 检查数据目录是否已存在
ls -la /var/lib/mysql/

# 如果数据目录不存在，创建它
sudo mkdir -p /var/lib/mysql
sudo chown -R mysql:mysql /var/lib/mysql
sudo chmod 750 /var/lib/mysql

# 创建日志目录
sudo mkdir -p /var/log/mysql
sudo chown -R mysql:mysql /var/log/mysql

# 创建PID文件目录
sudo mkdir -p /var/run/mysqld
sudo chown -R mysql:mysql /var/run/mysqld
```

### 3.2 创建MySQL配置文件
```bash
# 创建配置文件
sudo vim /etc/my.cnf
```

**配置文件内容（针对ARM64优化）：**
```ini
# MySQL 配置文件
# 该文件包含 MySQL 服务的核心配置参数

[mysqld]
#主设置
server_id = 1  # 主库唯一ID（不能和从库重复）
log_bin = mysql-bin  # 开启二进制日志
binlog_format = ROW  # 日志格式（ROW/STATEMENT/MIXED）
#binlog_do_db = mydb  # 只同步指定的数据库（可选）
#binlog_ignore_db = mysql  # 忽略系统库（可选）
binlog_ignore_db = information_schema
binlog_ignore_db = performance_schema
expire_logs_days = 7  # binlog 保存7天
max_binlog_size = 1G  # 每个binlog大小限制
sync_binlog = 1  # 每次事务提交都刷盘，保证数据安全
#从设置
#server_id = 2  # 从库唯一ID（不能和主库重复）
#log_bin = mysql-bin  # 从库建议也开启binlog（可选）
#relay_log = mysql-relay-bin  # 中继日志
#read_only = 1  # 从库设为只读（避免误操作）
#replicate_ignore_db = mysql  # 忽略同步的库（与主库一致）
#replicate_ignore_db = information_schema
#replicate_ignore_db = performance_schema
# MySQL 的安装目录
basedir=/usr/local/mysql

# 数据文件存储目录
datadir=/usr/local/mysql/data

# 本地通信的 Unix 套接字文件路径
socket=/usr/local/mysql/mysql.sock

# MySQL 监听的 IP 地址
# 设置为 0.0.0.0 允许远程连接，设置为 127.0.0.1 仅允许本地连接
bind-address = 0.0.0.0

# MySQL 监听的端口号
port=3306

# SQL 模式，定义 MySQL 的行为
# NO_ENGINE_SUBSTITUTION: 防止自动替换存储引擎
# STRICT_TRANS_TABLES: 严格模式，确保数据完整性
sql_mode=NO_ENGINE_SUBSTITUTION,STRICT_TRANS_TABLES

# 最大并发连接数
# 根据服务器性能调整，600 是一个较高的值，适合高并发场景
max_connections=600

# 最大连接错误次数
# 超过该次数后，MySQL 会拒绝来自该 IP 的连接
max_connect_errors=1000

# 默认时区
# 设置为东八区时区
default-time_zone = '+8:00'

# 为每个 InnoDB 表创建独立的表空间文件
# 便于管理和优化表空间，减少碎片
innodb_file_per_table=1

# 服务器默认字符集
# 支持 UTF-8 编码，适合多语言环境
character_set_server=utf8mb4

# 表名大小写敏感性
# 设置为 1，表名在存储和比较时忽略大小写
lower_case_table_names=1

# 错误日志文件路径
log-error=/usr/local/mysql/data/error.log

# 记录执行时间超过 2 秒的查询
slow_query_log = 1
slow_query_log_file = /usr/local/mysql/data/slow-query.log
long_query_time = 2  

# MySQL 进程 ID 文件路径
pid-file=/var/run/mysqld/mysqld.pid

# 禁用符号链接
# 防止安全风险
symbolic-links=0

# 禁用 InnoDB 严格模式
# 0 表示禁用严格模式，允许一些不符合规范的操作通过
innodb_strict_mode=0

[mysqld_safe]
# mysqld_safe 脚本的日志文件路径
log-error=/usr/local/mysql/data/mysqld_safe.log

# mysqld_safe 脚本的进程 ID 文件路径
pid-file=/usr/local/mysql/data/mysqld_safe.pid

[client]
socket=/usr/local/mysql/mysql.sock

```

### 3.3 创建日志目录
```bash
# 创建日志目录
sudo mkdir -p /var/log/mysql
sudo chown -R mysql:mysql /var/log/mysql
```

---

## 4. 服务启动和开机自启动

### 4.1 初始化MySQL数据库
```bash
# 初始化数据库（会生成临时root密码）
sudo /usr/local/mysql/bin/mysqld --initialize --user=mysql --datadir=/var/lib/mysql

# 或者如果使用YUM安装
sudo mysqld --initialize --user=mysql
```

**⚠️ 重要提示：** 初始化过程会在日志中生成临时root密码，请记录下来！

```bash
# 查看临时密码
sudo grep 'temporary password' /var/log/mysqld.log
```

### 4.2 创建systemd服务文件
```bash
# 创建服务文件
sudo vim /etc/systemd/system/mysqld.service
```

**服务文件内容：**
```ini
[Unit]
Description=MySQL Server
Documentation=man:mysqld(8)
Documentation=http://dev.mysql.com/doc/refman/en/using-systemd.html
After=network.target
After=syslog.target

[Install]
WantedBy=multi-user.target

[Service]
User=mysql
Group=mysql
Type=notify
TimeoutSec=0
PermissionsStartOnly=true
ExecStartPre=/usr/local/mysql/bin/mysqld_safe_helper
ExecStart=/usr/local/mysql/bin/mysqld --defaults-file=/etc/my.cnf
Restart=on-failure
RestartPreventExitStatus=1
Environment=MYSQLD_PARENT_PID=1
PrivateTmp=false
```

### 4.3 启动MySQL服务
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动MySQL服务
sudo systemctl start mysqld

# 检查服务状态
sudo systemctl status mysqld

# 设置开机自启动
sudo systemctl enable mysqld
```

---

## 5. 安全配置

### 5.1 运行MySQL安全配置脚本
```bash
# 运行安全配置向导
sudo /usr/local/mysql/bin/mysql_secure_installation

# 或者如果使用YUM安装
sudo mysql_secure_installation
```

**配置过程中的选择建议：**
1. 输入临时root密码
2. 设置新的root密码（建议使用强密码）
3. 移除匿名用户：`Y`
4. 禁止root远程登录：`Y`（根据需要选择）
5. 移除test数据库：`Y`
6. 重新加载权限表：`Y`

### 5.2 手动安全配置（可选）
```bash
# 登录MySQL
mysql -u root -p

# 在MySQL中执行以下命令
```

```sql
-- 修改root密码
ALTER USER 'root'@'localhost' IDENTIFIED BY 'your_strong_password';

-- 创建新的管理用户（推荐）
CREATE USER 'admin'@'localhost' IDENTIFIED BY 'admin_password';
GRANT ALL PRIVILEGES ON *.* TO 'admin'@'localhost' WITH GRANT OPTION;

-- 创建应用用户
CREATE USER 'appuser'@'localhost' IDENTIFIED BY 'app_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON your_database.* TO 'appuser'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

### 5.3 配置防火墙（如果需要远程访问）
```bash
# 检查防火墙状态
sudo firewall-cmd --state

# 开放MySQL端口
sudo firewall-cmd --permanent --add-port=3306/tcp
sudo firewall-cmd --reload

# 验证端口开放
sudo firewall-cmd --list-ports
```

---

## 6. ARM架构和麒麟系统特殊注意事项

### 6.1 ARM64架构兼容性问题

#### 6.1.1 内存对齐问题
```bash
# 检查系统页面大小
getconf PAGESIZE

# 如果页面大小为64KB，需要在配置文件中添加
echo "innodb_page_size = 64K" | sudo tee -a /etc/my.cnf
```

#### 6.1.2 性能优化建议
```bash
# ARM64处理器特有的优化设置
sudo vim /etc/my.cnf
```

**添加ARM64优化配置：**
```ini
# ARM64特有优化
innodb_use_native_aio = 1
innodb_flush_method = O_DIRECT
innodb_io_capacity = 200
innodb_io_capacity_max = 2000

# 根据ARM处理器核心数调整
innodb_read_io_threads = 4
innodb_write_io_threads = 4
innodb_purge_threads = 2
```

### 6.2 麒麟系统特有配置

#### 6.2.1 SELinux配置
```bash
# 检查SELinux状态
getenforce

# 如果启用了SELinux，需要配置相关策略
sudo setsebool -P mysql_connect_any 1
sudo semanage port -a -t mysqld_port_t -p tcp 3306
```

#### 6.2.2 麒麟系统包管理器使用
```bash
# 使用麒麟系统的包管理器安装依赖
sudo yum install -y kylin-mysql-libs

# 检查麒麟系统特有的MySQL相关包
yum search mysql | grep kylin
```

### 6.3 依赖问题解决方案

#### 6.3.1 常见依赖缺失
```bash
# 安装可能缺失的依赖
sudo yum install -y libaio libaio-devel
sudo yum install -y numactl numactl-devel
sudo yum install -y perl-Data-Dumper

# 检查依赖是否满足
ldd /usr/local/mysql/bin/mysqld
```

#### 6.3.2 库文件链接问题
```bash
# 创建必要的符号链接
sudo ln -s /usr/local/mysql/lib/libmysqlclient.so.21 /usr/lib64/
sudo ldconfig

# 更新动态链接库缓存
echo "/usr/local/mysql/lib" | sudo tee /etc/ld.so.conf.d/mysql.conf
sudo ldconfig
```

---

## 7. 验证步骤

### 7.1 服务状态验证
```bash
# 检查MySQL服务状态
sudo systemctl status mysqld

# 检查MySQL进程
ps aux | grep mysql

# 检查端口监听
netstat -tlnp | grep :3306
```

### 7.2 连接测试
```bash
# 本地连接测试
mysql -u root -p

# 检查MySQL版本
mysql -u root -p -e "SELECT VERSION();"

# 检查字符集设置
mysql -u root -p -e "SHOW VARIABLES LIKE 'character%';"
```

### 7.3 基本功能测试
```sql
-- 登录MySQL后执行以下测试
-- 创建测试数据库
CREATE DATABASE test_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用测试数据库
USE test_db;

-- 创建测试表
CREATE TABLE test_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入测试数据
INSERT INTO test_table (name) VALUES ('测试数据1'), ('测试数据2');

-- 查询测试数据
SELECT * FROM test_table;

-- 检查表结构
DESCRIBE test_table;

-- 清理测试数据
DROP DATABASE test_db;
```

### 7.4 性能基准测试
```bash
# 使用mysqlslap进行性能测试
/usr/local/mysql/bin/mysqlslap \
  --user=root \
  --password \
  --host=localhost \
  --concurrency=10 \
  --iterations=100 \
  --auto-generate-sql \
  --auto-generate-sql-load-type=mixed \
  --number-of-queries=1000
```

---

## 8. 常见问题排查

### 8.1 安装过程常见问题

#### 8.1.1 MariaDB与MySQL冲突问题
```bash
# 错误信息：file /etc/my.cnf from install of mysql-community-server conflicts with file from package mariadb-common
# 原因：系统中已安装MariaDB，与MySQL配置文件冲突

# 解决方案：
# 1. 检查MariaDB安装状态
rpm -qa | grep mariadb
systemctl status mariadb

# 2. 完全卸载MariaDB
sudo systemctl stop mariadb
sudo yum remove -y mariadb-server mariadb mariadb-common mariadb-libs mariadb-connector-c
sudo rm -f /etc/my.cnf

# 3. 清理缓存并重新安装MySQL
sudo yum clean all
sudo yum install -y mysql-community-server --nogpgcheck

# 4. 验证安装
rpm -qa | grep mysql-community
```

#### 8.1.2 GPG密钥验证失败
```bash
# 错误信息：GPG 检查失败
# 原因：MySQL包的GPG密钥验证失败

# 解决方案1：跳过GPG检查（推荐）
sudo yum install -y mysql-community-server --nogpgcheck

# 解决方案2：手动导入密钥
wget https://repo.mysql.com/RPM-GPG-KEY-mysql-2022
sudo rpm --import RPM-GPG-KEY-mysql-2022
sudo yum install -y mysql-community-server
```

#### 8.1.3 阿里云镜像404错误
```bash
# 错误信息：404 Not Found when downloading from mirrors.aliyun.com
# 原因：阿里云镜像路径结构与官方不同

# 解决方案1：使用官方源+修改方法
wget https://dev.mysql.com/get/mysql80-community-release-el8-1.noarch.rpm
sudo rpm -Uvh mysql80-community-release-el8-1.noarch.rpm
sudo sed -i 's|repo.mysql.com|mirrors.aliyun.com/mysql|g' /etc/yum.repos.d/mysql-community.repo

# 解决方案2：手动创建配置文件
sudo tee /etc/yum.repos.d/mysql-community.repo > /dev/null <<EOF
[mysql80-community]
name=MySQL 8.0 Community Server
baseurl=https://mirrors.aliyun.com/mysql/yum/mysql-8.0-community/el/8/\$basearch/
enabled=1
gpgcheck=1
gpgkey=https://repo.mysql.com/RPM-GPG-KEY-mysql-2022
EOF
```

#### 8.1.2 网络连接问题
```bash
# 测试网络连通性
ping -c 3 mirrors.aliyun.com
ping -c 3 repo.mysql.com

# 测试HTTPS连接
curl -I https://mirrors.aliyun.com/mysql/yum/mysql-8.0-community/el/8/aarch64/
curl -I https://repo.mysql.com/yum/mysql-8.0-community/el/8/aarch64/

# 如果都无法连接，检查防火墙和代理设置
```

### 8.2 启动失败问题

#### 8.2.1 检查错误日志
```bash
# 查看MySQL错误日志
sudo tail -f /var/log/mysqld.log

# 查看系统日志
sudo journalctl -u mysqld -f
```

#### 8.1.2 常见启动错误及解决方案

**错误1：权限问题**
```bash
# 错误信息：Can't create/write to file '/var/lib/mysql/...' (Errcode: 13 - Permission denied)
# 解决方案：
sudo chown -R mysql:mysql /var/lib/mysql
sudo chmod 750 /var/lib/mysql
```

**错误2：端口占用**
```bash
# 错误信息：Can't start server: Bind on TCP/IP port: Address already in use
# 解决方案：
sudo netstat -tlnp | grep :3306
sudo kill -9 <PID>
```

**错误3：内存不足**
```bash
# 错误信息：Cannot allocate memory for the buffer pool
# 解决方案：调整innodb_buffer_pool_size
sudo vim /etc/my.cnf
# 将innodb_buffer_pool_size调整为更小的值，如512M
```

### 8.2 连接问题

#### 8.2.1 无法连接到MySQL
```bash
# 检查服务是否运行
sudo systemctl status mysqld

# 检查端口是否监听
sudo netstat -tlnp | grep :3306

# 检查防火墙设置
sudo firewall-cmd --list-ports
```

#### 8.2.2 密码认证问题
```sql
-- 如果遇到认证插件问题
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'your_password';
FLUSH PRIVILEGES;
```

### 8.3 ARM64特有问题

#### 8.3.1 二进制包不兼容
```bash
# 检查二进制文件架构
file /usr/local/mysql/bin/mysqld

# 如果架构不匹配，重新下载正确的ARM64版本
wget https://dev.mysql.com/get/Downloads/MySQL-8.0/mysql-8.0.35-linux-glibc2.28-aarch64.tar.xz
```

#### 8.3.2 性能问题
```bash
# 检查CPU信息
cat /proc/cpuinfo

# 根据CPU核心数调整配置
# 编辑/etc/my.cnf，调整线程相关参数
```

### 8.4 麒麟系统特有问题

#### 8.4.1 包依赖冲突
```bash
# 清理包缓存
sudo yum clean all

# 重新安装有问题的包
sudo yum reinstall mysql-community-server
```

#### 8.4.2 SELinux相关问题
```bash
# 检查SELinux日志
sudo ausearch -m avc -ts recent | grep mysql

# 临时禁用SELinux（仅用于测试）
sudo setenforce 0

# 永久禁用SELinux（不推荐）
sudo vim /etc/selinux/config
# 设置SELINUX=disabled
```

---

## 9. 维护和监控

### 9.1 日常维护命令
```bash
# 检查MySQL状态
sudo systemctl status mysqld

# 查看MySQL进程
ps aux | grep mysql

# 检查磁盘使用情况
du -sh /var/lib/mysql

# 备份数据库
mysqldump -u root -p --all-databases > backup_$(date +%Y%m%d).sql
```

### 9.2 性能监控
```sql
-- 查看当前连接数
SHOW STATUS LIKE 'Threads_connected';

-- 查看慢查询
SHOW STATUS LIKE 'Slow_queries';

-- 查看缓冲池使用情况
SHOW STATUS LIKE 'Innodb_buffer_pool%';
```

### 9.3 日志轮转配置
```bash
# 创建日志轮转配置
sudo vim /etc/logrotate.d/mysql
```

```
/var/log/mysqld.log {
    daily
    missingok
    rotate 52
    compress
    notifempty
    create 640 mysql mysql
    postrotate
        if test -x /usr/bin/mysqladmin && \
           /usr/bin/mysqladmin ping &>/dev/null
        then
           /usr/bin/mysqladmin flush-logs
        fi
    endscript
}
```

---

## 10. 总结

本教程提供了在ARM64架构的银河麒麟V10 SP3系统上安装MySQL 8.0的完整指南。主要包括：

1. **多种安装方式**：官方YUM源、二进制包、源码编译
2. **ARM64优化**：针对ARM架构的特殊配置和优化
3. **麒麟系统适配**：处理麒麟系统特有的配置需求
4. **安全配置**：完整的安全设置和用户权限管理
5. **问题排查**：常见问题的诊断和解决方案

**重要提醒：**
- 安装前请备份重要数据
- 根据实际硬件配置调整内存相关参数
- 定期更新MySQL版本以获得安全补丁
- 建立定期备份策略

如有问题，请参考官方文档或寻求技术支持。
