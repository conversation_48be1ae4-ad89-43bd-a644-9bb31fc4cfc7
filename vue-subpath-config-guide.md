# Vue.js 子路径部署配置指南

## 问题分析

您遇到的问题是典型的Vue单页应用子路径部署问题：

### 问题现象
- 访问 `https://51.onefind.club:8081/hot/` 时
- CSS文件请求路径：`https://51.onefind.club:8081/assets/index-C3ukZKY5.css` ❌
- JS文件请求路径：`https://51.onefind.club:8081/assets/index-DFt-fc80.js` ❌

### 正确路径应该是
- CSS文件请求路径：`https://51.onefind.club:8081/hot/assets/index-C3ukZKY5.css` ✅
- JS文件请求路径：`https://51.onefind.club:8081/hot/assets/index-DFt-fc80.js` ✅

## 解决方案

### 1. Vue项目配置修改

在Vue项目根目录创建或修改 `vue.config.js` 文件：

```javascript
// vue.config.js
const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  // 设置公共路径为子路径
  publicPath: process.env.NODE_ENV === 'production' ? '/hot/' : '/',
  
  // 输出目录
  outputDir: 'dist',
  
  // 静态资源目录
  assetsDir: 'assets',
  
  // 生产环境关闭source map
  productionSourceMap: false,
  
  // 开发服务器配置
  devServer: {
    port: 3000,
    open: true,
    // 如果需要代理API
    proxy: {
      '/interface-server': {
        target: 'http://localhost:6007',
        changeOrigin: true,
        pathRewrite: {
          '^/interface-server': ''
        }
      }
    }
  },
  
  // 配置webpack
  configureWebpack: {
    // 可以在这里添加额外的webpack配置
  }
})
```

### 2. 如果使用Vite构建工具

在 `vite.config.js` 中配置：

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  
  // 设置基础路径
  base: process.env.NODE_ENV === 'production' ? '/hot/' : '/',
  
  // 构建配置
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false
  },
  
  // 开发服务器配置
  server: {
    port: 3000,
    open: true,
    // API代理配置
    proxy: {
      '/interface-server': {
        target: 'http://localhost:6007',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/interface-server/, '')
      }
    }
  }
})
```

### 3. 路由配置修改

如果使用Vue Router，需要修改路由配置：

```javascript
// router/index.js
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  // 设置基础路径
  history: createWebHistory(process.env.BASE_URL || '/hot/'),
  routes: [
    // 你的路由配置
  ]
})

export default router
```

### 4. API请求配置

在Vue项目中配置API请求基础路径：

```javascript
// utils/request.js 或 api/index.js
import axios from 'axios'

// 创建axios实例
const request = axios.create({
  // 设置API基础路径
  baseURL: '/interface-server',
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 可以在这里添加token等
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

export default request
```

## 部署步骤

### 1. 修改Vue项目配置
```bash
# 在Vue项目根目录
cd /path/to/your/vue-project

# 创建或修改vue.config.js
vim vue.config.js

# 添加上述配置内容
```

### 2. 重新构建项目
```bash
# 清理之前的构建
rm -rf dist/

# 重新安装依赖（如果需要）
npm install

# 构建生产版本
npm run build
```

### 3. 部署到服务器
```bash
# 备份现有文件
sudo cp -r /opt/haoju/web/hot /opt/haoju/web/hot.backup.$(date +%Y%m%d_%H%M%S)

# 清空目标目录
sudo rm -rf /opt/haoju/web/hot/*

# 复制新构建的文件
sudo cp -r dist/* /opt/haoju/web/hot/

# 设置正确的权限
sudo chown -R nginx:nginx /opt/haoju/web/hot
sudo chmod -R 755 /opt/haoju/web/hot
```

### 4. 验证部署
```bash
# 检查文件是否正确部署
ls -la /opt/haoju/web/hot/

# 检查index.html中的资源路径
grep -E "(href|src)=" /opt/haoju/web/hot/index.html

# 重新加载Nginx配置
sudo nginx -s reload

# 运行排查脚本
chmod +x nginx-vue-troubleshoot.sh
./nginx-vue-troubleshoot.sh
```

## 验证方法

### 1. 检查构建后的文件
构建完成后，检查 `dist/index.html` 文件中的资源引用：

```html
<!-- 正确的引用应该是这样的 -->
<link rel="stylesheet" href="/hot/assets/index-C3ukZKY5.css">
<script src="/hot/assets/index-DFt-fc80.js"></script>

<!-- 而不是这样的 -->
<link rel="stylesheet" href="/assets/index-C3ukZKY5.css">
<script src="/assets/index-DFt-fc80.js"></script>
```

### 2. 浏览器测试
1. 打开浏览器开发者工具
2. 访问 `https://51.onefind.club:8081/hot/`
3. 检查Network标签页，确认所有资源都能正确加载
4. 检查Console标签页，确认没有404或MIME类型错误

### 3. 命令行测试
```bash
# 测试主页
curl -I https://51.onefind.club:8081/hot/

# 测试CSS文件（替换为实际文件名）
curl -I https://51.onefind.club:8081/hot/assets/index-C3ukZKY5.css

# 测试JS文件（替换为实际文件名）
curl -I https://51.onefind.club:8081/hot/assets/index-DFt-fc80.js
```

## 常见问题解决

### 问题1：资源仍然使用绝对路径
**解决方案**：确保 `publicPath` 或 `base` 配置正确，重新构建项目

### 问题2：路由跳转404
**解决方案**：检查Vue Router的base配置，确保与publicPath一致

### 问题3：API请求失败
**解决方案**：检查API代理配置，确保baseURL正确

### 问题4：开发环境正常，生产环境异常
**解决方案**：检查环境变量配置，确保生产环境使用正确的publicPath

## 最佳实践

1. **使用环境变量**：区分开发和生产环境的配置
2. **相对路径**：尽量使用相对路径而不是绝对路径
3. **测试验证**：每次部署后都要进行完整的功能测试
4. **备份策略**：部署前备份现有文件
5. **日志监控**：定期检查Nginx访问和错误日志

按照以上步骤操作，您的Vue应用应该能够正确加载所有静态资源。
