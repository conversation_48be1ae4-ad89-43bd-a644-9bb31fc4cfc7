# Apache Tomcat WAR包部署在 ARM64 银河麒麟 V10 SP3 系统指南

## 目录
1. [系统环境检查和准备工作](#1-系统环境检查和准备工作)
2. [Java环境验证](#2-java环境验证)
3. [Tomcat下载和安装](#3-tomcat下载和安装)
4. [Tomcat配置和优化](#4-tomcat配置和优化)
5. [WAR包部署方法](#5-war包部署方法)
6. [服务管理配置](#6-服务管理配置)
7. [虚拟主机和域名配置](#7-虚拟主机和域名配置)
8. [SSL/HTTPS配置](#8-ssl-https配置)
9. [数据库连接池配置](#9-数据库连接池配置)
10. [性能调优和JVM配置](#10-性能调优和jvm配置)
11. [监控和日志管理](#11-监控和日志管理)
12. [故障排查指南](#12-故障排查指南)
13. [备份和回滚策略](#13-备份和回滚策略)
14. [ARM架构和麒麟系统特殊注意事项](#14-arm架构和麒麟系统特殊注意事项)

---

## 1. 系统环境检查和准备工作

### 1.1 检查系统信息
```bash
# 检查操作系统版本
cat /etc/os-release

# 检查处理器架构
uname -m

# 检查内核版本
uname -r

# 检查可用内存
free -h

# 检查磁盘空间
df -h
```

**预期输出示例：**
- 架构应显示：`aarch64`
- 操作系统：`Kylin Linux V10 SP3`

### 1.2 更新系统包
```bash
# 更新包管理器缓存
sudo yum update -y

# 安装必要的工具
sudo yum install -y wget curl vim tar net-tools unzip
```

### 1.3 检查端口占用
```bash
# 检查Tomcat默认端口是否被占用
# 8080 - HTTP端口
# 8443 - HTTPS端口
# 8005 - 关闭端口
# 8009 - AJP端口
netstat -tuln | grep -E "(8080|8443|8005|8009)"

# 如果端口被占用，查看占用进程
sudo lsof -i :8080
sudo lsof -i :8443
```

---

## 2. Java环境验证

### 2.1 检查Java安装
```bash
# 检查Java版本（需要Java 8或更高版本）
java -version

# 检查JAVA_HOME环境变量
echo $JAVA_HOME

# 检查javac编译器
javac -version
```

**⚠️ 重要提醒：**
- Tomcat 9.x 需要 Java 8 或更高版本
- 如果未安装Java，请参考 `Java8_ARM64_Kylin_Installation_Guide.md` 文档

### 2.2 设置Java环境变量（如果未设置）
```bash
# 查找Java安装路径
ls -la /usr/lib/jvm/

# 设置JAVA_HOME（根据实际路径调整）
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export PATH=$JAVA_HOME/bin:$PATH

# 永久设置环境变量
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

---

## 3. Tomcat下载和安装

### 3.1 创建Tomcat目录
```bash
# 创建安装目录
sudo mkdir -p /usr/local/tomcat
sudo mkdir -p /var/log/tomcat
sudo mkdir -p /var/lib/tomcat

# 设置目录权限（使用root用户）
sudo chmod 755 /usr/local/tomcat
sudo chmod 755 /var/log/tomcat
sudo chmod 755 /var/lib/tomcat
```

### 3.2 下载Tomcat
```bash
# 创建下载目录
mkdir -p ~/tomcat-install && cd ~/tomcat-install

# 下载Tomcat 9.0.84（最新稳定版本，兼容Java 8）
# 如果需要使用代理，设置代理环境变量
export http_proxy=http://127.0.0.1:7897
export https_proxy=http://127.0.0.1:7897

wget https://archive.apache.org/dist/tomcat/tomcat-9/v9.0.84/bin/apache-tomcat-9.0.84.tar.gz

# 验证下载文件
ls -la apache-tomcat-9.0.84.tar.gz

# 可选：验证文件完整性
wget https://archive.apache.org/dist/tomcat/tomcat-9/v9.0.84/bin/apache-tomcat-9.0.84.tar.gz.sha512
sha512sum -c apache-tomcat-9.0.84.tar.gz.sha512
```

### 3.3 解压和安装
```bash
# 解压Tomcat
tar -xzf apache-tomcat-9.0.84.tar.gz

# 移动到安装目录
sudo mv apache-tomcat-9.0.84/* /usr/local/tomcat/

# 设置目录权限
sudo chmod -R 755 /usr/local/tomcat

# 创建符号链接便于管理
sudo ln -s /usr/local/tomcat /usr/local/apache-tomcat

# 设置可执行权限
sudo chmod +x /usr/local/tomcat/bin/*.sh
```

---

## 4. Tomcat配置和优化

### 4.1 主配置文件设置
```bash
# 备份原始配置文件
sudo cp /usr/local/tomcat/conf/server.xml /usr/local/tomcat/conf/server.xml.backup

# 编辑主配置文件
sudo vim /usr/local/tomcat/conf/server.xml
```

**关键配置优化：**
```xml
<!-- 优化连接器配置 -->
<Connector port="8080" protocol="HTTP/1.1"
           connectionTimeout="20000"
           redirectPort="8443"
           maxThreads="200"
           minSpareThreads="10"
           maxSpareThreads="75"
           acceptCount="100"
           enableLookups="false"
           compression="on"
           compressionMinSize="2048"
           compressableMimeType="text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json"
           URIEncoding="UTF-8" />

<!-- 优化AJP连接器（如果使用Apache前端） -->
<Connector protocol="AJP/1.3"
           address="::1"
           port="8009"
           redirectPort="8443"
           maxThreads="200"
           connectionTimeout="20000" />
```

### 4.2 JVM参数配置
```bash
# 创建setenv.sh文件用于JVM参数配置
sudo tee /usr/local/tomcat/bin/setenv.sh > /dev/null <<'EOF'
#!/bin/bash
# Tomcat JVM配置 - ARM64优化

# 基本内存设置
export CATALINA_OPTS="$CATALINA_OPTS -Xms1024m -Xmx2048m"

# ARM64架构优化的JVM参数
export CATALINA_OPTS="$CATALINA_OPTS -server"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseG1GC"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseStringDeduplication"
export CATALINA_OPTS="$CATALINA_OPTS -XX:MaxGCPauseMillis=200"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+OptimizeStringConcat"

# 系统属性设置
export CATALINA_OPTS="$CATALINA_OPTS -Djava.awt.headless=true"
export CATALINA_OPTS="$CATALINA_OPTS -Djava.net.preferIPv4Stack=true"
export CATALINA_OPTS="$CATALINA_OPTS -Dfile.encoding=UTF-8"
export CATALINA_OPTS="$CATALINA_OPTS -Duser.timezone=Asia/Shanghai"

# JMX监控配置（可选）
export CATALINA_OPTS="$CATALINA_OPTS -Dcom.sun.management.jmxremote"
export CATALINA_OPTS="$CATALINA_OPTS -Dcom.sun.management.jmxremote.port=9999"
export CATALINA_OPTS="$CATALINA_OPTS -Dcom.sun.management.jmxremote.ssl=false"
export CATALINA_OPTS="$CATALINA_OPTS -Dcom.sun.management.jmxremote.authenticate=false"

# 日志配置
export CATALINA_OPTS="$CATALINA_OPTS -Djava.util.logging.config.file=$CATALINA_BASE/conf/logging.properties"
export CATALINA_OPTS="$CATALINA_OPTS -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager"

# 临时目录设置
export CATALINA_OPTS="$CATALINA_OPTS -Djava.io.tmpdir=/var/lib/tomcat"
EOF

# 设置可执行权限
sudo chmod +x /usr/local/tomcat/bin/setenv.sh
```

### 4.3 用户和角色配置
```bash
# 编辑用户配置文件
sudo vim /usr/local/tomcat/conf/tomcat-users.xml
```

**添加管理用户：**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<tomcat-users xmlns="http://tomcat.apache.org/xml"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://tomcat.apache.org/xml tomcat-users.xsd"
              version="1.0">

  <!-- 定义角色 -->
  <role rolename="manager-gui"/>
  <role rolename="manager-script"/>
  <role rolename="manager-jmx"/>
  <role rolename="manager-status"/>
  <role rolename="admin-gui"/>
  <role rolename="admin-script"/>

  <!-- 定义用户 -->
  <user username="admin" password="admin123" roles="manager-gui,manager-script,manager-jmx,manager-status,admin-gui,admin-script"/>
  <user username="deployer" password="deploy123" roles="manager-script"/>
</tomcat-users>
```

### 4.4 管理应用访问配置
```bash
# 配置Manager应用允许远程访问
sudo vim /usr/local/tomcat/webapps/manager/META-INF/context.xml
```

**注释掉IP限制（生产环境请谨慎配置）：**
```xml
<Context antiResourceLocking="false" privileged="true" >
  <CookieProcessor className="org.apache.tomcat.util.http.Rfc6265CookieProcessor"
                   sameSiteCookies="strict" />
  <!--
  <Valve className="org.apache.catalina.valves.RemoteAddrValve"
         allow="127\.\d+\.\d+\.\d+|::1|0:0:0:0:0:0:0:1" />
  -->
  <Manager sessionAttributeValueClassNameFilter="java\.lang\.(?:Boolean|Integer|Long|Number|String)|org\.apache\.catalina\.filters\.CsrfPreventionFilter\$LruCache(?:\$1)?|java\.util\.(?:Linked)?HashMap"/>
</Context>
```

---

## 5. WAR包部署方法

### 5.1 手动部署方法

#### 5.1.1 直接复制部署
```bash
# 停止Tomcat服务
sudo /usr/local/tomcat/bin/shutdown.sh

# 复制WAR包到webapps目录
sudo cp /path/to/your/application.war /usr/local/tomcat/webapps/

# 启动Tomcat服务
sudo /usr/local/tomcat/bin/startup.sh

# 检查部署状态
tail -f /usr/local/tomcat/logs/catalina.out
```

#### 5.1.2 解压部署
```bash
# 创建应用目录
sudo mkdir -p /usr/local/tomcat/webapps/myapp

# 解压WAR包到应用目录
cd /usr/local/tomcat/webapps/myapp
sudo unzip /path/to/your/application.war

# 设置权限
sudo chmod -R 755 /usr/local/tomcat/webapps/myapp
```

### 5.2 热部署方法

#### 5.2.1 通过Manager应用部署
```bash
# 使用curl通过Manager应用部署
curl -u admin:admin123 -T /path/to/your/application.war \
  "http://localhost:8080/manager/text/deploy?path=/myapp&update=true"

# 检查应用状态
curl -u admin:admin123 "http://localhost:8080/manager/text/list"
```

#### 5.2.2 自动部署配置
```bash
# 编辑Host配置启用自动部署
sudo vim /usr/local/tomcat/conf/server.xml
```

**配置自动部署：**
```xml
<Host name="localhost" appBase="webapps"
      unpackWARs="true" autoDeploy="true"
      deployOnStartup="true">
  
  <!-- 配置自动部署目录 -->
  <Context docBase="/usr/local/tomcat/webapps/myapp" path="/myapp" reloadable="true"/>
  
</Host>
```

### 5.3 多环境部署配置
```bash
# 创建不同环境的配置目录
sudo mkdir -p /usr/local/tomcat/conf/environments/{dev,test,prod}

# 为每个环境创建配置文件
sudo tee /usr/local/tomcat/conf/environments/prod/context.xml > /dev/null <<'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<Context>
    <!-- 生产环境数据库配置 -->
    <Resource name="jdbc/MyDB" auth="Container" type="javax.sql.DataSource"
              maxTotal="100" maxIdle="30" maxWaitMillis="10000"
              username="prod_user" password="prod_password"
              driverClassName="com.mysql.cj.jdbc.Driver"
              url="*******************************************"/>
</Context>
EOF
```

---

## 6. 服务管理配置

### 6.1 创建systemd服务文件
```bash
# 创建Tomcat服务文件
sudo tee /etc/systemd/system/tomcat.service > /dev/null <<EOF
[Unit]
Description=Apache Tomcat Web Application Container
After=network.target

[Service]
Type=forking
ExecStart=/usr/local/tomcat/bin/startup.sh
ExecStop=/usr/local/tomcat/bin/shutdown.sh
ExecReload=/usr/local/tomcat/bin/shutdown.sh && /usr/local/tomcat/bin/startup.sh
PIDFile=/usr/local/tomcat/temp/tomcat.pid
Restart=on-failure
RestartSec=10

Environment=JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
Environment=CATALINA_HOME=/usr/local/tomcat
Environment=CATALINA_BASE=/usr/local/tomcat
Environment=CATALINA_TMPDIR=/var/lib/tomcat
Environment=CATALINA_PID=/usr/local/tomcat/temp/tomcat.pid

[Install]
WantedBy=multi-user.target
EOF
```

### 6.2 启动和管理服务
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动Tomcat服务
sudo systemctl start tomcat

# 检查服务状态
sudo systemctl status tomcat

# 设置开机自启动
sudo systemctl enable tomcat

# 其他管理命令
sudo systemctl stop tomcat      # 停止服务
sudo systemctl restart tomcat   # 重启服务
sudo systemctl disable tomcat   # 禁用开机自启动
```

### 6.3 手动启动和停止（可选）
```bash
# 手动启动Tomcat
sudo /usr/local/tomcat/bin/startup.sh

# 手动停止Tomcat
sudo /usr/local/tomcat/bin/shutdown.sh

# 查看Tomcat进程
ps aux | grep tomcat

# 检查Tomcat日志
tail -f /usr/local/tomcat/logs/catalina.out
```

---

## 7. 虚拟主机和域名配置

### 7.1 配置虚拟主机
```bash
# 编辑server.xml配置虚拟主机
sudo vim /usr/local/tomcat/conf/server.xml
```

**添加虚拟主机配置：**
```xml
<Engine name="Catalina" defaultHost="localhost">

  <!-- 默认主机 -->
  <Host name="localhost" appBase="webapps"
        unpackWARs="true" autoDeploy="true">
    <Valve className="org.apache.catalina.valves.AccessLogValve" directory="logs"
           prefix="localhost_access_log" suffix=".txt"
           pattern="%h %l %u %t &quot;%r&quot; %s %b" />
  </Host>

  <!-- 自定义域名主机 -->
  <Host name="myapp.example.com" appBase="webapps-myapp"
        unpackWARs="true" autoDeploy="true">

    <!-- 应用上下文配置 -->
    <Context path="" docBase="myapp" reloadable="true">
      <!-- 数据源配置 -->
      <Resource name="jdbc/MyAppDB" auth="Container" type="javax.sql.DataSource"
                maxTotal="20" maxIdle="5" maxWaitMillis="10000"
                username="myapp_user" password="myapp_password"
                driverClassName="com.mysql.cj.jdbc.Driver"
                url="*********************************"/>
    </Context>

    <!-- 访问日志 -->
    <Valve className="org.apache.catalina.valves.AccessLogValve" directory="logs"
           prefix="myapp_access_log" suffix=".txt"
           pattern="%h %l %u %t &quot;%r&quot; %s %b %D" />
  </Host>

</Engine>
```

### 7.2 创建虚拟主机目录
```bash
# 创建虚拟主机应用目录
sudo mkdir -p /usr/local/tomcat/webapps-myapp
sudo chmod 755 /usr/local/tomcat/webapps-myapp

# 部署应用到虚拟主机
sudo cp /path/to/myapp.war /usr/local/tomcat/webapps-myapp/
```

---

## 8. SSL/HTTPS配置

### 8.1 生成SSL证书
```bash
# 创建证书存储目录
sudo mkdir -p /usr/local/tomcat/ssl

# 生成自签名证书（测试用）
sudo keytool -genkey -alias tomcat -keyalg RSA -keysize 2048 -validity 365 \
  -keystore /usr/local/tomcat/ssl/tomcat.keystore \
  -storepass changeit -keypass changeit \
  -dname "CN=localhost, OU=IT, O=MyCompany, L=City, ST=State, C=CN"

# 设置证书文件权限
sudo chmod 600 /usr/local/tomcat/ssl/tomcat.keystore
```

### 8.2 配置HTTPS连接器
```bash
# 编辑server.xml添加HTTPS连接器
sudo vim /usr/local/tomcat/conf/server.xml
```

**添加HTTPS连接器配置：**
```xml
<!-- HTTPS连接器配置 -->
<Connector port="8443" protocol="org.apache.coyote.http11.Http11NioProtocol"
           maxThreads="150" SSLEnabled="true">
  <UpgradeProtocol className="org.apache.coyote.http2.Http2Protocol" />
  <SSLHostConfig>
    <Certificate certificateKeystoreFile="/usr/local/tomcat/ssl/tomcat.keystore"
                 certificateKeystorePassword="changeit"
                 type="RSA" />
  </SSLHostConfig>
</Connector>
```

### 8.3 强制HTTPS重定向
```bash
# 编辑web.xml配置强制HTTPS
sudo vim /usr/local/tomcat/conf/web.xml
```

**在web.xml末尾添加：**
```xml
<!-- 强制HTTPS访问 -->
<security-constraint>
  <web-resource-collection>
    <web-resource-name>Protected Context</web-resource-name>
    <url-pattern>/*</url-pattern>
  </web-resource-collection>
  <user-data-constraint>
    <transport-guarantee>CONFIDENTIAL</transport-guarantee>
  </user-data-constraint>
</security-constraint>
```

---

## 9. 数据库连接池配置

### 9.1 MySQL连接池配置
```bash
# 下载MySQL JDBC驱动
cd /usr/local/tomcat/lib
sudo wget https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar
```

### 9.2 配置全局数据源
```bash
# 编辑context.xml配置全局数据源
sudo vim /usr/local/tomcat/conf/context.xml
```

**添加数据源配置：**
```xml
<Context>
    <!-- MySQL数据源配置 -->
    <Resource name="jdbc/MyDB" auth="Container" type="javax.sql.DataSource"
              maxTotal="100" maxIdle="30" maxWaitMillis="10000"
              username="myapp_user" password="myapp_password"
              driverClassName="com.mysql.cj.jdbc.Driver"
              url="*********************************?useSSL=false&amp;serverTimezone=Asia/Shanghai&amp;characterEncoding=utf8"
              validationQuery="SELECT 1"
              testOnBorrow="true"
              testWhileIdle="true"
              timeBetweenEvictionRunsMillis="30000"
              minEvictableIdleTimeMillis="60000" />

    <!-- Redis连接池配置（如果使用） -->
    <Resource name="redis/MyRedis" auth="Container"
              type="redis.clients.jedis.JedisPool"
              factory="org.apache.naming.factory.BeanFactory"
              host="127.0.0.1"
              port="6379"
              password="your_redis_password"
              maxTotal="100"
              maxIdle="30"
              minIdle="10"
              testOnBorrow="true" />
</Context>
```

### 9.3 应用级数据源配置
```bash
# 在应用的META-INF/context.xml中配置
sudo tee /usr/local/tomcat/webapps/myapp/META-INF/context.xml > /dev/null <<'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<Context>
    <!-- 应用专用数据源 -->
    <Resource name="jdbc/AppDB" auth="Container" type="javax.sql.DataSource"
              maxTotal="50" maxIdle="20" maxWaitMillis="5000"
              username="app_user" password="app_password"
              driverClassName="com.mysql.cj.jdbc.Driver"
              url="*******************************************************************************"
              validationQuery="SELECT 1"
              testOnBorrow="true" />
</Context>
EOF
```

---

## 10. 性能调优和JVM配置

### 10.1 JVM内存调优
```bash
# 编辑setenv.sh进行JVM调优
sudo vim /usr/local/tomcat/bin/setenv.sh
```

**针对ARM64的JVM优化配置：**
```bash
#!/bin/bash
# Tomcat JVM性能调优配置 - ARM64优化

# 根据系统内存调整堆内存大小
# 系统内存4GB: -Xms2g -Xmx3g
# 系统内存8GB: -Xms4g -Xmx6g
# 系统内存16GB: -Xms8g -Xmx12g
export CATALINA_OPTS="$CATALINA_OPTS -Xms2g -Xmx4g"

# 新生代内存配置
export CATALINA_OPTS="$CATALINA_OPTS -XX:NewRatio=3"
export CATALINA_OPTS="$CATALINA_OPTS -XX:SurvivorRatio=8"

# ARM64优化的垃圾收集器配置
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseG1GC"
export CATALINA_OPTS="$CATALINA_OPTS -XX:MaxGCPauseMillis=200"
export CATALINA_OPTS="$CATALINA_OPTS -XX:G1HeapRegionSize=16m"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseStringDeduplication"

# JIT编译器优化
export CATALINA_OPTS="$CATALINA_OPTS -XX:+OptimizeStringConcat"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseCompressedOops"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseCompressedClassPointers"

# GC日志配置
export CATALINA_OPTS="$CATALINA_OPTS -Xloggc:/var/log/tomcat/gc.log"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintGCDetails"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintGCTimeStamps"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseGCLogFileRotation"
export CATALINA_OPTS="$CATALINA_OPTS -XX:NumberOfGCLogFiles=5"
export CATALINA_OPTS="$CATALINA_OPTS -XX:GCLogFileSize=10M"

# 系统属性优化
export CATALINA_OPTS="$CATALINA_OPTS -Djava.awt.headless=true"
export CATALINA_OPTS="$CATALINA_OPTS -Djava.net.preferIPv4Stack=true"
export CATALINA_OPTS="$CATALINA_OPTS -Dfile.encoding=UTF-8"
export CATALINA_OPTS="$CATALINA_OPTS -Duser.timezone=Asia/Shanghai"
export CATALINA_OPTS="$CATALINA_OPTS -Djava.security.egd=file:/dev/./urandom"
```

### 10.2 Tomcat连接器调优
```bash
# 编辑server.xml优化连接器
sudo vim /usr/local/tomcat/conf/server.xml
```

**高性能连接器配置：**
```xml
<!-- 高性能HTTP连接器配置 -->
<Connector port="8080" protocol="org.apache.coyote.http11.Http11NioProtocol"
           connectionTimeout="20000"
           redirectPort="8443"
           maxThreads="400"
           minSpareThreads="25"
           maxSpareThreads="100"
           acceptCount="200"
           maxConnections="1000"
           enableLookups="false"
           compression="on"
           compressionMinSize="2048"
           compressableMimeType="text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json,application/xml"
           URIEncoding="UTF-8"
           useBodyEncodingForURI="true"
           maxHttpHeaderSize="8192"
           maxPostSize="2097152"
           processorCache="400"
           tcpNoDelay="true" />
```

### 10.3 系统级优化
```bash
# 优化系统参数
sudo tee -a /etc/sysctl.conf > /dev/null <<EOF
# Tomcat性能优化
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_keepalive_probes = 3
net.ipv4.tcp_keepalive_intvl = 15
vm.swappiness = 1
EOF

# 应用系统参数
sudo sysctl -p

# 优化文件描述符限制
echo '* soft nofile 65535' | sudo tee -a /etc/security/limits.conf
echo '* hard nofile 65535' | sudo tee -a /etc/security/limits.conf
```

---

## 11. 监控和日志管理

### 11.1 日志配置
```bash
# 配置日志轮转
sudo tee /etc/logrotate.d/tomcat > /dev/null <<EOF
/usr/local/tomcat/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    notifempty
    create 644 root root
    postrotate
        /bin/kill -USR1 \$(cat /usr/local/tomcat/temp/tomcat.pid 2>/dev/null) 2>/dev/null || true
    endscript
}

/var/log/tomcat/*.log {
    daily
    missingok
    rotate 30
    compress
    notifempty
    create 644 root root
}
EOF
```

### 11.2 JMX监控配置
```bash
# 编辑setenv.sh添加JMX监控
sudo vim /usr/local/tomcat/bin/setenv.sh
```

**添加JMX配置：**
```bash
# JMX监控配置
export CATALINA_OPTS="$CATALINA_OPTS -Dcom.sun.management.jmxremote"
export CATALINA_OPTS="$CATALINA_OPTS -Dcom.sun.management.jmxremote.port=9999"
export CATALINA_OPTS="$CATALINA_OPTS -Dcom.sun.management.jmxremote.ssl=false"
export CATALINA_OPTS="$CATALINA_OPTS -Dcom.sun.management.jmxremote.authenticate=false"
export CATALINA_OPTS="$CATALINA_OPTS -Djava.rmi.server.hostname=localhost"
```

### 11.3 应用监控脚本
```bash
# 创建Tomcat监控脚本
sudo tee /usr/local/bin/tomcat-monitor.sh > /dev/null <<'EOF'
#!/bin/bash
# Tomcat监控脚本

TOMCAT_HOME="/usr/local/tomcat"
LOG_FILE="/var/log/tomcat-monitor.log"
PID_FILE="$TOMCAT_HOME/temp/tomcat.pid"

# 检查Tomcat进程
if ! pgrep -f "catalina" > /dev/null; then
    echo "$(date): Tomcat进程未运行，尝试重启..." >> $LOG_FILE
    systemctl restart tomcat
fi

# 检查HTTP端口
if ! netstat -tuln | grep ":8080" > /dev/null; then
    echo "$(date): Tomcat端口8080未监听，尝试重启..." >> $LOG_FILE
    systemctl restart tomcat
fi

# 检查应用响应
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/ 2>/dev/null)
if [ "$HTTP_STATUS" != "200" ] && [ "$HTTP_STATUS" != "302" ]; then
    echo "$(date): Tomcat HTTP响应异常 (状态码: $HTTP_STATUS)，尝试重启..." >> $LOG_FILE
    systemctl restart tomcat
fi

# 检查内存使用
if [ -f "$PID_FILE" ]; then
    PID=$(cat $PID_FILE)
    if [ -n "$PID" ]; then
        MEMORY_USAGE=$(ps -p $PID -o %mem --no-headers 2>/dev/null | tr -d ' ')
        if [ -n "$MEMORY_USAGE" ]; then
            echo "$(date): Tomcat内存使用率: ${MEMORY_USAGE}%" >> $LOG_FILE
            if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
                echo "$(date): Tomcat内存使用率过高: ${MEMORY_USAGE}%" >> $LOG_FILE
            fi
        fi
    fi
fi

# 检查日志文件大小
CATALINA_LOG="$TOMCAT_HOME/logs/catalina.out"
if [ -f "$CATALINA_LOG" ]; then
    LOG_SIZE=$(du -m "$CATALINA_LOG" | cut -f1)
    if [ "$LOG_SIZE" -gt 100 ]; then
        echo "$(date): catalina.out日志文件过大: ${LOG_SIZE}MB" >> $LOG_FILE
    fi
fi
EOF

sudo chmod +x /usr/local/bin/tomcat-monitor.sh

# 添加到crontab
echo "*/5 * * * * /usr/local/bin/tomcat-monitor.sh" | sudo crontab -
```

---

## 12. 故障排查指南

### 12.1 启动问题排查

#### 12.1.1 Java环境问题
```bash
# 检查Java版本和JAVA_HOME
java -version
echo $JAVA_HOME

# 检查Tomcat启动脚本中的Java路径
grep JAVA_HOME /usr/local/tomcat/bin/catalina.sh
```

#### 12.1.2 端口占用问题
```bash
# 检查端口占用
sudo lsof -i :8080
sudo netstat -tuln | grep 8080

# 杀死占用端口的进程
sudo kill -9 <PID>

# 或者修改Tomcat端口
sudo vim /usr/local/tomcat/conf/server.xml
# 修改 <Connector port="8080" 为其他端口
```

#### 12.1.3 权限问题
```bash
# 检查Tomcat目录权限
ls -la /usr/local/tomcat/
ls -la /usr/local/tomcat/bin/

# 修复权限问题
sudo chmod -R 755 /usr/local/tomcat
sudo chmod +x /usr/local/tomcat/bin/*.sh
```

### 12.2 应用部署问题

#### 12.2.1 WAR包部署失败
```bash
# 检查应用部署日志
tail -f /usr/local/tomcat/logs/catalina.out
tail -f /usr/local/tomcat/logs/localhost.*.log

# 检查应用目录权限
ls -la /usr/local/tomcat/webapps/
ls -la /usr/local/tomcat/webapps/myapp/

# 手动解压WAR包检查
cd /tmp
unzip -l /path/to/application.war
```

#### 12.2.2 数据库连接问题
```bash
# 检查数据库连接
mysql -u myapp_user -p -h localhost myapp

# 检查JDBC驱动
ls -la /usr/local/tomcat/lib/mysql-connector-java*.jar

# 检查数据源配置
grep -n "jdbc" /usr/local/tomcat/conf/context.xml
```

### 12.3 性能问题排查

#### 12.3.1 内存问题
```bash
# 检查JVM内存使用
jstat -gc $(pgrep -f catalina)
jmap -histo $(pgrep -f catalina) | head -20

# 生成堆转储文件
jmap -dump:format=b,file=/tmp/tomcat-heap.hprof $(pgrep -f catalina)
```

#### 12.3.2 线程问题
```bash
# 检查线程状态
jstack $(pgrep -f catalina) > /tmp/tomcat-threads.txt

# 检查连接数
netstat -an | grep :8080 | wc -l
```

### 12.4 日志分析
```bash
# 分析访问日志
tail -f /usr/local/tomcat/logs/localhost_access_log.*.txt

# 分析错误日志
grep -i error /usr/local/tomcat/logs/catalina.out
grep -i exception /usr/local/tomcat/logs/catalina.out

# 分析GC日志
tail -f /var/log/tomcat/gc.log
```

---

## 13. 备份和回滚策略

### 13.1 应用备份策略
```bash
# 创建备份脚本
sudo tee /usr/local/bin/tomcat-backup.sh > /dev/null <<'EOF'
#!/bin/bash
# Tomcat应用备份脚本

BACKUP_DIR="/backup/tomcat"
TOMCAT_HOME="/usr/local/tomcat"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份webapps目录
echo "备份webapps目录..."
tar -czf $BACKUP_DIR/webapps_$DATE.tar.gz -C $TOMCAT_HOME webapps/

# 备份配置文件
echo "备份配置文件..."
tar -czf $BACKUP_DIR/conf_$DATE.tar.gz -C $TOMCAT_HOME conf/

# 备份日志文件
echo "备份日志文件..."
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz -C $TOMCAT_HOME logs/

# 清理7天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "备份完成: $DATE"
EOF

sudo chmod +x /usr/local/bin/tomcat-backup.sh

# 设置定时备份
echo "0 2 * * * /usr/local/bin/tomcat-backup.sh >> /var/log/tomcat-backup.log 2>&1" | sudo crontab -
```

### 13.2 应用回滚策略
```bash
# 创建回滚脚本
sudo tee /usr/local/bin/tomcat-rollback.sh > /dev/null <<'EOF'
#!/bin/bash
# Tomcat应用回滚脚本

if [ $# -ne 2 ]; then
    echo "用法: $0 <应用名> <备份日期>"
    echo "示例: $0 myapp 20231210_140000"
    exit 1
fi

APP_NAME=$1
BACKUP_DATE=$2
BACKUP_DIR="/backup/tomcat"
TOMCAT_HOME="/usr/local/tomcat"

# 检查备份文件是否存在
if [ ! -f "$BACKUP_DIR/webapps_$BACKUP_DATE.tar.gz" ]; then
    echo "错误: 备份文件不存在 $BACKUP_DIR/webapps_$BACKUP_DATE.tar.gz"
    exit 1
fi

# 停止Tomcat
echo "停止Tomcat服务..."
systemctl stop tomcat

# 备份当前版本
echo "备份当前版本..."
CURRENT_DATE=$(date +%Y%m%d_%H%M%S)
tar -czf $BACKUP_DIR/rollback_backup_$CURRENT_DATE.tar.gz -C $TOMCAT_HOME webapps/$APP_NAME

# 删除当前应用
echo "删除当前应用..."
rm -rf $TOMCAT_HOME/webapps/$APP_NAME
rm -f $TOMCAT_HOME/webapps/$APP_NAME.war

# 恢复备份版本
echo "恢复备份版本..."
cd $TOMCAT_HOME
tar -xzf $BACKUP_DIR/webapps_$BACKUP_DATE.tar.gz

# 启动Tomcat
echo "启动Tomcat服务..."
systemctl start tomcat

echo "回滚完成"
EOF

sudo chmod +x /usr/local/bin/tomcat-rollback.sh
```

### 13.3 数据库备份集成
```bash
# 创建完整备份脚本（包含数据库）
sudo tee /usr/local/bin/full-backup.sh > /dev/null <<'EOF'
#!/bin/bash
# 完整备份脚本（应用+数据库）

BACKUP_DIR="/backup/full"
DATE=$(date +%Y%m%d_%H%M%S)
DB_USER="backup_user"
DB_PASSWORD="backup_password"
DB_NAME="myapp"

mkdir -p $BACKUP_DIR

# 备份数据库
echo "备份数据库..."
mysqldump -u $DB_USER -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/database_$DATE.sql

# 备份应用
echo "备份应用..."
/usr/local/bin/tomcat-backup.sh

# 创建完整备份包
echo "创建完整备份包..."
tar -czf $BACKUP_DIR/full_backup_$DATE.tar.gz -C /backup tomcat/ -C $BACKUP_DIR database_$DATE.sql

# 清理临时文件
rm -f $BACKUP_DIR/database_$DATE.sql

echo "完整备份完成: $DATE"
EOF

sudo chmod +x /usr/local/bin/full-backup.sh
```

---

## 14. ARM架构和麒麟系统特殊注意事项

### 14.1 ARM64架构优化

#### 14.1.1 JVM参数调优
```bash
# ARM64特有的JVM优化参数
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UnlockExperimentalVMOptions"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseZGC"  # 如果Java版本支持
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseLargePages"
export CATALINA_OPTS="$CATALINA_OPTS -XX:LargePageSizeInBytes=2m"
```

#### 14.1.2 网络优化
```bash
# 针对ARM64的网络参数优化
echo 'net.core.rmem_max = 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 87380 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 134217728' | sudo tee -a /etc/sysctl.conf

# 应用网络参数
sudo sysctl -p
```

### 14.2 麒麟系统特有配置

#### 14.2.1 防火墙配置
```bash
# 开放Tomcat端口
sudo firewall-cmd --permanent --add-port=8080/tcp   # HTTP端口
sudo firewall-cmd --permanent --add-port=8443/tcp   # HTTPS端口
sudo firewall-cmd --permanent --add-port=8005/tcp   # 关闭端口（仅本地）
sudo firewall-cmd --permanent --add-port=8009/tcp   # AJP端口
sudo firewall-cmd --permanent --add-port=9999/tcp   # JMX端口

# 重新加载防火墙规则
sudo firewall-cmd --reload

# 查看开放的端口
sudo firewall-cmd --list-ports
```

#### 14.2.2 SELinux配置
```bash
# 检查SELinux状态
getenforce

# 如果启用了SELinux，配置相关策略
sudo setsebool -P httpd_can_network_connect 1
sudo setsebool -P tomcat_can_network_connect_db 1

# 为Tomcat目录设置SELinux上下文
sudo semanage fcontext -a -t bin_t "/usr/local/tomcat/bin/*"
sudo semanage fcontext -a -t lib_t "/usr/local/tomcat/lib/*"
sudo restorecon -R /usr/local/tomcat/
```

---

## 总结

本教程提供了在ARM64架构的银河麒麟V10 SP3系统上部署Tomcat和WAR包应用的完整指南。主要包括：

1. **完整安装流程**：从系统准备到Tomcat配置的详细步骤
2. **多种部署方式**：手动部署、热部署、自动部署等方法
3. **服务管理**：systemd服务配置和管理
4. **高级配置**：虚拟主机、SSL、数据库连接池配置
5. **性能优化**：针对ARM64架构的JVM和系统参数调优
6. **监控维护**：日志管理、监控脚本和故障排查
7. **备份策略**：完整的备份和回滚解决方案

**重要提醒：**
- 根据实际负载调整JVM内存参数
- 生产环境务必配置SSL和安全认证
- 定期备份应用和配置文件
- 监控Tomcat性能指标和系统资源使用
- 及时更新Tomcat版本以获得安全补丁

如有问题，请参考官方文档或寻求技术支持。
