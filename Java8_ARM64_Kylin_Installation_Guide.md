# Java 8 在 ARM64 银河麒麟 V10 SP3 系统安装教程

## 目录
1. [系统环境检查和准备工作](#1-系统环境检查和准备工作)
2. [Java 8 下载和安装](#2-java-8-下载和安装)
3. [环境变量配置](#3-环境变量配置)
4. [验证安装](#4-验证安装)
5. [多版本Java管理](#5-多版本java管理)
6. [ARM架构和麒麟系统特殊注意事项](#6-arm架构和麒麟系统特殊注意事项)
7. [常见问题排查](#7-常见问题排查)

---

## 1. 系统环境检查和准备工作

### 1.1 检查系统信息
```bash
# 检查操作系统版本
cat /etc/os-release

# 检查处理器架构
uname -m

# 检查内核版本
uname -r

# 检查可用内存
free -h

# 检查磁盘空间
df -h
```

**预期输出示例：**
- 架构应显示：`aarch64`
- 操作系统：`Kylin Linux V10 SP3`

### 1.2 检查现有Java安装
```bash
# 检查是否已安装Java
java -version

# 检查JAVA_HOME环境变量
echo $JAVA_HOME

# 查看已安装的Java包
rpm -qa | grep -i java

# 查看系统中的Java可执行文件
which java
whereis java
```

### 1.3 更新系统包
```bash
# 更新包管理器缓存
sudo yum update -y

# 安装必要的工具
sudo yum install -y wget curl vim tar
```

---

## 2. Java 8 下载和安装

### 2.1 方法一：使用YUM包管理器安装（推荐）

#### 2.1.1 安装OpenJDK 8
```bash
# 搜索可用的Java 8包
yum search openjdk | grep "1.8"

# 安装OpenJDK 8开发工具包
sudo yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 验证安装
java -version
javac -version
```

#### 2.1.2 安装Oracle JDK 8（如果需要）
```bash
# 注意：Oracle JDK需要从官网手动下载
# 由于许可证限制，无法通过包管理器直接安装
echo "Oracle JDK需要手动下载，请参考方法二"
```

### 2.2 方法二：手动下载安装包

#### 2.2.1 下载OpenJDK 8
```bash
# 创建下载目录
mkdir -p ~/java-install && cd ~/java-install

# 下载OpenJDK 8 ARM64版本
# 使用Adoptium（原AdoptOpenJDK）镜像
wget https://github.com/adoptium/temurin8-binaries/releases/download/jdk8u392-b08/OpenJDK8U-jdk_aarch64_linux_hotspot_8u392b08.tar.gz

# 验证下载文件
ls -la OpenJDK8U-jdk_aarch64_linux_hotspot_8u392b08.tar.gz
```

#### 2.2.2 解压和安装
```bash
# 解压JDK
tar -xzf OpenJDK8U-jdk_aarch64_linux_hotspot_8u392b08.tar.gz

# 移动到标准安装目录
sudo mv jdk8u392-b08 /opt/java8

# 设置目录权限
sudo chown -R root:root /opt/java8
sudo chmod -R 755 /opt/java8
```

### 2.3 方法三：使用SDKMAN管理Java版本

#### 2.3.1 安装SDKMAN
```bash
# 安装SDKMAN
curl -s "https://get.sdkman.io" | bash

# 重新加载shell
source "$HOME/.sdkman/bin/sdkman-init.sh"

# 验证SDKMAN安装
sdk version
```

#### 2.3.2 使用SDKMAN安装Java 8
```bash
# 列出可用的Java版本
sdk list java | grep "8\."

# 安装Java 8（选择适合ARM64的版本）
sdk install java 8.0.392-tem

# 设置为默认版本
sdk default java 8.0.392-tem

# 验证安装
java -version
```

---

## 3. 环境变量配置

### 3.1 配置JAVA_HOME和PATH

#### 3.1.1 确定Java安装路径
```bash
# 如果使用YUM安装的OpenJDK
ls -la /usr/lib/jvm/

# 如果手动安装到/opt
ls -la /opt/java8/

# 如果使用SDKMAN
echo $JAVA_HOME
```

#### 3.1.2 配置系统级环境变量
```bash
# 创建Java环境配置文件
sudo tee /etc/profile.d/java.sh > /dev/null <<EOF
# Java 8 Environment Variables
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export JRE_HOME=\$JAVA_HOME/jre
export PATH=\$JAVA_HOME/bin:\$JRE_HOME/bin:\$PATH
export CLASSPATH=.:\$JAVA_HOME/lib:\$JRE_HOME/lib
EOF

# 使配置生效
source /etc/profile.d/java.sh

# 验证环境变量
echo $JAVA_HOME
echo $PATH | grep java
```

#### 3.1.3 配置用户级环境变量（可选）
```bash
# 编辑用户的.bashrc文件
vim ~/.bashrc

# 添加以下内容到文件末尾：
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export JRE_HOME=$JAVA_HOME/jre
export PATH=$JAVA_HOME/bin:$JRE_HOME/bin:$PATH
export CLASSPATH=.:$JAVA_HOME/lib:$JRE_HOME/lib

# 重新加载配置
source ~/.bashrc
```

### 3.2 配置alternatives（多版本管理）
```bash
# 配置java命令的alternatives
sudo alternatives --install /usr/bin/java java /usr/lib/jvm/java-1.8.0-openjdk/bin/java 1

# 配置javac命令的alternatives
sudo alternatives --install /usr/bin/javac javac /usr/lib/jvm/java-1.8.0-openjdk/bin/javac 1

# 配置默认Java版本
sudo alternatives --config java
sudo alternatives --config javac
```

---

## 4. 验证安装

### 4.1 基本验证
```bash
# 检查Java版本
java -version

# 检查Java编译器版本
javac -version

# 检查环境变量
echo "JAVA_HOME: $JAVA_HOME"
echo "JRE_HOME: $JRE_HOME"
echo "PATH: $PATH"

# 检查Java可执行文件位置
which java
which javac
```

### 4.2 功能测试
```bash
# 创建测试目录
mkdir -p ~/java-test && cd ~/java-test

# 创建简单的Java程序
cat > HelloWorld.java << 'EOF'
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
        System.out.println("Java Version: " + System.getProperty("java.version"));
        System.out.println("Java Vendor: " + System.getProperty("java.vendor"));
        System.out.println("OS Name: " + System.getProperty("os.name"));
        System.out.println("OS Arch: " + System.getProperty("os.arch"));
    }
}
EOF

# 编译Java程序
javac HelloWorld.java

# 运行Java程序
java HelloWorld

# 清理测试文件
cd ~ && rm -rf ~/java-test
```

### 4.3 性能测试
```bash
# 检查JVM信息
java -XX:+PrintFlagsFinal -version | grep -i "UseG1GC\|UseParallelGC"

# 检查可用的垃圾收集器
java -XX:+UnlockExperimentalVMOptions -XX:+PrintGCDetails -version

# 简单的内存测试
java -Xmx512m -Xms256m -version
```

---

## 5. 多版本Java管理

### 5.1 使用alternatives管理多版本
```bash
# 查看当前配置的Java版本
sudo alternatives --display java

# 添加新的Java版本到alternatives
sudo alternatives --install /usr/bin/java java /path/to/new/java/bin/java 2

# 选择默认Java版本
sudo alternatives --config java

# 查看所有配置的Java版本
sudo alternatives --list | grep java
```

### 5.2 使用update-alternatives（Debian系统风格）
```bash
# 如果系统支持update-alternatives
sudo update-alternatives --install /usr/bin/java java /usr/lib/jvm/java-1.8.0-openjdk/bin/java 1

# 配置默认版本
sudo update-alternatives --config java
```

### 5.3 手动切换Java版本
```bash
# 创建Java版本切换脚本
sudo tee /usr/local/bin/switch-java > /dev/null <<'EOF'
#!/bin/bash
case "$1" in
    8)
        export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
        ;;
    11)
        export JAVA_HOME=/usr/lib/jvm/java-11-openjdk
        ;;
    *)
        echo "Usage: switch-java {8|11}"
        exit 1
        ;;
esac
export PATH=$JAVA_HOME/bin:$PATH
echo "Switched to Java $1"
java -version
EOF

# 设置执行权限
sudo chmod +x /usr/local/bin/switch-java

# 使用示例
switch-java 8
```

---

## 6. ARM架构和麒麟系统特殊注意事项

### 6.1 ARM64架构兼容性

#### 6.1.1 JVM参数优化
```bash
# ARM64特有的JVM优化参数
export JAVA_OPTS="-server -Xmx2g -Xms1g -XX:+UseG1GC -XX:+UseStringDeduplication"

# 针对ARM64的垃圾收集器选择
export JAVA_OPTS="$JAVA_OPTS -XX:+UnlockExperimentalVMOptions -XX:+UseZGC"
```

#### 6.1.2 性能调优建议
```bash
# 检查CPU信息
cat /proc/cpuinfo | grep -E "(processor|model name|cpu cores)"

# 根据CPU核心数调整并行GC线程
export JAVA_OPTS="$JAVA_OPTS -XX:ParallelGCThreads=4"

# 设置堆内存大小（根据系统内存调整）
export JAVA_OPTS="$JAVA_OPTS -Xmx4g -Xms2g"
```

### 6.2 麒麟系统特有配置

#### 6.2.1 字体配置
```bash
# 安装中文字体支持
sudo yum install -y dejavu-fonts-common dejavu-sans-fonts dejavu-serif-fonts

# 配置Java字体
sudo mkdir -p $JAVA_HOME/jre/lib/fonts/fallback
sudo ln -s /usr/share/fonts/dejavu/DejaVuSans.ttf $JAVA_HOME/jre/lib/fonts/fallback/
```

#### 6.2.2 安全策略配置
```bash
# 检查SELinux状态
getenforce

# 如果启用了SELinux，配置Java相关策略
sudo setsebool -P allow_java_execstack 1

# 配置防火墙（如果需要Java应用监听端口）
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

---

## 7. 常见问题排查

### 7.1 安装问题

#### 7.1.1 包依赖问题
```bash
# 错误：依赖包缺失
# 解决方案：安装依赖包
sudo yum install -y glibc libgcc

# 检查依赖
ldd $JAVA_HOME/bin/java
```

#### 7.1.2 权限问题
```bash
# 错误：权限不足
# 解决方案：修复权限
sudo chown -R root:root $JAVA_HOME
sudo chmod -R 755 $JAVA_HOME
```

### 7.2 环境变量问题

#### 7.2.1 JAVA_HOME未设置
```bash
# 检查问题
echo $JAVA_HOME

# 解决方案：重新设置环境变量
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc
source ~/.bashrc
```

#### 7.2.2 PATH配置错误
```bash
# 检查PATH
echo $PATH | grep java

# 修复PATH
export PATH=$JAVA_HOME/bin:$PATH
```

### 7.3 运行时问题

#### 7.3.1 内存不足
```bash
# 错误：OutOfMemoryError
# 解决方案：调整堆内存
java -Xmx2g -Xms1g YourApplication
```

#### 7.3.2 字符编码问题
```bash
# 设置默认字符编码
export JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF-8"
```

---

## 8. 总结

本教程提供了在ARM64架构的银河麒麟V10 SP3系统上安装Java 8的完整指南。主要包括：

1. **多种安装方式**：YUM包管理器、手动安装、SDKMAN管理
2. **环境配置**：系统级和用户级环境变量配置
3. **多版本管理**：alternatives和手动切换方法
4. **ARM64优化**：针对ARM架构的JVM参数调优
5. **问题排查**：常见问题的诊断和解决方案

**重要提醒：**
- 建议优先使用YUM包管理器安装OpenJDK 8
- 正确配置环境变量对Java应用运行至关重要
- 根据实际应用需求调整JVM参数
- 定期更新Java版本以获得安全补丁

如有问题，请参考官方文档或寻求技术支持。
