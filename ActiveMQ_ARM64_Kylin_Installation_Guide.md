# Apache ActiveMQ 在 ARM64 银河麒麟 V10 SP3 系统安装教程

## 目录
1. [系统环境检查和准备工作](#1-系统环境检查和准备工作)
2. [Java环境验证](#2-java环境验证)
3. [ActiveMQ下载和安装](#3-activemq下载和安装)
4. [配置文件设置和优化](#4-配置文件设置和优化)
5. [服务启动和管理](#5-服务启动和管理)
6. [Spring Boot集成配置](#6-spring-boot集成配置)
7. [验证安装](#7-验证安装)
8. [ARM架构和麒麟系统特殊注意事项](#8-arm架构和麒麟系统特殊注意事项)
9. [常见问题排查](#9-常见问题排查)
10. [性能优化建议](#10-性能优化建议)

---

## 1. 系统环境检查和准备工作

### 1.1 检查系统信息
```bash
# 检查操作系统版本
cat /etc/os-release

# 检查处理器架构
uname -m

# 检查内核版本
uname -r

# 检查可用内存
free -h

# 检查磁盘空间
df -h
```

**预期输出示例：**
- 架构应显示：`aarch64`
- 操作系统：`Kylin Linux V10 SP3`

### 1.2 更新系统包
```bash
# 更新包管理器缓存
sudo yum update -y

# 安装必要的工具
sudo yum install -y wget curl vim tar net-tools
```

### 1.3 检查端口占用
```bash
# 检查ActiveMQ默认端口是否被占用
# 61616 - JMS端口
# 8161 - Web管理控制台端口
netstat -tuln | grep -E "(61616|8161)"

# 如果端口被占用，查看占用进程
sudo lsof -i :61616
sudo lsof -i :8161
```

---

## 2. Java环境验证

### 2.1 检查Java安装
```bash
# 检查Java版本（需要Java 8或更高版本）
java -version

# 检查JAVA_HOME环境变量
echo $JAVA_HOME

# 检查javac编译器
javac -version
```

**⚠️ 重要提醒：**
- ActiveMQ 5.x 需要 Java 8 或更高版本
- 如果未安装Java，请参考 `Java8_ARM64_Kylin_Installation_Guide.md` 文档

### 2.2 设置Java环境变量（如果未设置）
```bash
# 查找Java安装路径
ls -la /usr/lib/jvm/

# 设置JAVA_HOME（根据实际路径调整）
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export PATH=$JAVA_HOME/bin:$PATH

# 永久设置环境变量
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

---

## 3. ActiveMQ下载和安装

### 3.1 创建ActiveMQ目录
```bash
# 创建安装目录
sudo mkdir -p /usr/local/activemq
sudo mkdir -p /var/log/activemq
sudo mkdir -p /var/lib/activemq

# 设置目录权限（使用root用户）
sudo chmod 755 /usr/local/activemq
sudo chmod 755 /var/log/activemq
sudo chmod 755 /var/lib/activemq
```

### 3.2 下载ActiveMQ
```bash
# 创建下载目录
mkdir -p ~/activemq-install && cd ~/activemq-install

# 下载ActiveMQ 5.18.3（最新稳定版本）
# 如果需要使用代理，设置代理环境变量
export http_proxy=http://127.0.0.1:7897
export https_proxy=http://127.0.0.1:7897

wget https://archive.apache.org/dist/activemq/5.18.3/apache-activemq-5.18.3-bin.tar.gz

# 验证下载文件
ls -la apache-activemq-5.18.3-bin.tar.gz

# 可选：验证文件完整性
wget https://archive.apache.org/dist/activemq/5.18.3/apache-activemq-5.18.3-bin.tar.gz.sha512
sha512sum -c apache-activemq-5.18.3-bin.tar.gz.sha512
```

### 3.3 解压和安装
```bash
# 解压ActiveMQ
tar -xzf apache-activemq-5.18.3-bin.tar.gz

# 移动到安装目录
sudo mv apache-activemq-5.18.3/* /usr/local/activemq/

# 设置目录权限
sudo chmod -R 755 /usr/local/activemq

# 创建符号链接便于管理
sudo ln -s /usr/local/activemq /usr/local/apache-activemq

# 设置可执行权限
sudo chmod +x /usr/local/activemq/bin/activemq
```

---

## 4. 配置文件设置和优化

### 4.1 主配置文件设置
```bash
# 备份原始配置文件
sudo cp /usr/local/activemq/conf/activemq.xml /usr/local/activemq/conf/activemq.xml.backup

# 编辑主配置文件
sudo vim /usr/local/activemq/conf/activemq.xml
```

**配置示例（关键部分）：**
```xml
<broker xmlns="http://activemq.apache.org/schema/core" 
        brokerName="localhost" 
        dataDirectory="${activemq.data}"
        schedulerSupport="true">

    <destinationPolicy>
        <policyMap>
            <policyEntries>
                <policyEntry topic=">" >
                    <pendingMessageLimitStrategy>
                        <constantPendingMessageLimitStrategy limit="1000"/>
                    </pendingMessageLimitStrategy>
                </policyEntry>
            </policyEntries>
        </policyMap>
    </destinationPolicy>

    <managementContext>
        <managementContext createConnector="false"/>
    </managementContext>

    <persistenceAdapter>
        <kahaDB directory="${activemq.data}/kahadb"/>
    </persistenceAdapter>

    <systemUsage>
        <systemUsage>
            <memoryUsage>
                <memoryUsage percentOfJvmHeap="70" />
            </memoryUsage>
            <storeUsage>
                <storeUsage limit="100 gb"/>
            </storeUsage>
            <tempUsage>
                <tempUsage limit="50 gb"/>
            </tempUsage>
        </systemUsage>
    </systemUsage>

    <transportConnectors>
        <transportConnector name="openwire" uri="tcp://0.0.0.0:61616?maximumConnections=1000&amp;wireFormat.maxFrameSize=104857600"/>
        <transportConnector name="amqp" uri="amqp://0.0.0.0:5672?maximumConnections=1000&amp;wireFormat.maxFrameSize=104857600"/>
        <transportConnector name="stomp" uri="stomp://0.0.0.0:61613?maximumConnections=1000&amp;wireFormat.maxFrameSize=104857600"/>
        <transportConnector name="mqtt" uri="mqtt://0.0.0.0:1883?maximumConnections=1000&amp;wireFormat.maxFrameSize=104857600"/>
        <transportConnector name="ws" uri="ws://0.0.0.0:61614?maximumConnections=1000&amp;wireFormat.maxFrameSize=104857600"/>
    </transportConnectors>

    <shutdownHooks>
        <bean xmlns="http://www.springframework.org/schema/beans" 
              class="org.apache.activemq.hooks.SpringContextHook" />
    </shutdownHooks>

</broker>
```

### 4.2 JVM参数配置
```bash
# 编辑JVM配置文件
sudo vim /usr/local/activemq/bin/env
```

**添加ARM64优化的JVM参数：**
```bash
# ARM64架构优化的JVM参数
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -Xms1G -Xmx2G"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:+UseG1GC"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:+UseStringDeduplication"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:MaxGCPauseMillis=200"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -Djava.awt.headless=true"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -Djava.net.preferIPv4Stack=true"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -Dcom.sun.management.jmxremote"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -Dcom.sun.management.jmxremote.port=1099"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -Dcom.sun.management.jmxremote.rmi.port=1099"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -Dcom.sun.management.jmxremote.ssl=false"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -Dcom.sun.management.jmxremote.authenticate=false"
```

### 4.3 Web管理控制台配置
```bash
# 编辑Web控制台配置
sudo vim /usr/local/activemq/conf/jetty.xml
```

**配置Web控制台端口和安全设置：**
```xml
<bean id="jettyPort" class="org.apache.activemq.web.WebConsolePort" init-method="start">
    <property name="host" value="0.0.0.0"/>
    <property name="port" value="8161"/>
</bean>
```

### 4.4 用户权限配置
```bash
# 编辑用户配置文件
sudo vim /usr/local/activemq/conf/users.properties
```

**添加管理用户：**
```properties
# 格式：username=password
admin=admin123
user=user123
```

```bash
# 编辑组配置文件
sudo vim /usr/local/activemq/conf/groups.properties
```

**配置用户组：**
```properties
# 格式：group=username1,username2
admins=admin
users=user
```

---

## 5. 服务启动和管理

### 5.1 创建systemd服务文件
```bash
# 创建ActiveMQ服务文件
sudo tee /etc/systemd/system/activemq.service > /dev/null <<EOF
[Unit]
Description=Apache ActiveMQ Message Broker
After=network.target

[Service]
Type=forking
ExecStart=/usr/local/activemq/bin/activemq start
ExecStop=/usr/local/activemq/bin/activemq stop
ExecReload=/usr/local/activemq/bin/activemq restart
PIDFile=/var/lib/activemq/activemq.pid
Restart=on-failure
RestartSec=10

Environment=JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
Environment=ACTIVEMQ_HOME=/usr/local/activemq
Environment=ACTIVEMQ_BASE=/usr/local/activemq
Environment=ACTIVEMQ_CONF=/usr/local/activemq/conf
Environment=ACTIVEMQ_DATA=/var/lib/activemq

[Install]
WantedBy=multi-user.target
EOF
```

### 5.2 启动和管理服务
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动ActiveMQ服务
sudo systemctl start activemq

# 检查服务状态
sudo systemctl status activemq

# 设置开机自启动
sudo systemctl enable activemq

# 其他管理命令
sudo systemctl stop activemq      # 停止服务
sudo systemctl restart activemq   # 重启服务
sudo systemctl disable activemq   # 禁用开机自启动
```

### 5.3 手动启动和停止（可选）
```bash
# 手动启动ActiveMQ
sudo /usr/local/activemq/bin/activemq start

# 手动停止ActiveMQ
sudo /usr/local/activemq/bin/activemq stop

# 查看ActiveMQ状态
sudo /usr/local/activemq/bin/activemq status

# 查看ActiveMQ进程
ps aux | grep activemq
```

---

## 6. Spring Boot集成配置

### 6.1 Maven依赖配置
```xml
<!-- 在pom.xml中添加ActiveMQ依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-activemq</artifactId>
</dependency>

<!-- 如果需要连接池支持 -->
<dependency>
    <groupId>org.apache.activemq</groupId>
    <artifactId>activemq-pool</artifactId>
</dependency>
```

### 6.2 Spring Boot配置文件
```yaml
# application.yml配置示例
spring:
  activemq:
    broker-url: tcp://localhost:61616
    user: admin
    password: admin123
    pool:
      enabled: true
      max-connections: 50
      idle-timeout: 30000
    packages:
      trust-all: false
      trusted: com.yourcompany.model

# JMS配置
  jms:
    template:
      default-destination: test.queue
      delivery-mode: persistent
      priority: 100
      qos-enabled: true
      receive-timeout: 1000
      time-to-live: 36000
```

### 6.3 Java配置类示例
```java
@Configuration
@EnableJms
public class ActiveMQConfig {

    @Value("${spring.activemq.broker-url}")
    private String brokerUrl;

    @Value("${spring.activemq.user}")
    private String user;

    @Value("${spring.activemq.password}")
    private String password;

    @Bean
    public ActiveMQConnectionFactory connectionFactory() {
        ActiveMQConnectionFactory connectionFactory = new ActiveMQConnectionFactory();
        connectionFactory.setBrokerURL(brokerUrl);
        connectionFactory.setUserName(user);
        connectionFactory.setPassword(password);
        return connectionFactory;
    }

    @Bean
    public JmsTemplate jmsTemplate() {
        JmsTemplate template = new JmsTemplate();
        template.setConnectionFactory(connectionFactory());
        template.setDefaultDestinationName("test.queue");
        return template;
    }

    @Bean
    public DefaultJmsListenerContainerFactory jmsListenerContainerFactory() {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory());
        factory.setConcurrency("1-1");
        return factory;
    }
}
```

---

## 7. 验证安装

### 7.1 基本连接测试
```bash
# 检查ActiveMQ进程
ps aux | grep activemq

# 检查端口监听状态
netstat -tuln | grep -E "(61616|8161)"

# 检查日志文件
tail -f /usr/local/activemq/data/activemq.log

# 测试JMS连接
telnet localhost 61616
```

### 7.2 Web管理控制台验证
```bash
# 访问Web管理控制台
# 在浏览器中打开：http://your-server-ip:8161/admin
# 默认用户名：admin
# 默认密码：admin123

# 使用curl测试Web控制台
curl -u admin:admin123 http://localhost:8161/admin/
```

### 7.3 JMS功能测试
```bash
# 创建测试目录
mkdir -p ~/activemq-test && cd ~/activemq-test

# 创建简单的Java测试程序
cat > ActiveMQTest.java << 'EOF'
import javax.jms.*;
import org.apache.activemq.ActiveMQConnectionFactory;

public class ActiveMQTest {
    public static void main(String[] args) throws Exception {
        // 创建连接工厂
        ConnectionFactory factory = new ActiveMQConnectionFactory("tcp://localhost:61616");

        // 创建连接
        Connection connection = factory.createConnection();
        connection.start();

        // 创建会话
        Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);

        // 创建队列
        Destination destination = session.createQueue("test.queue");

        // 创建生产者
        MessageProducer producer = session.createProducer(destination);

        // 发送消息
        TextMessage message = session.createTextMessage("Hello ActiveMQ!");
        producer.send(message);
        System.out.println("消息已发送: " + message.getText());

        // 创建消费者
        MessageConsumer consumer = session.createConsumer(destination);

        // 接收消息
        Message receivedMessage = consumer.receive(1000);
        if (receivedMessage instanceof TextMessage) {
            TextMessage textMessage = (TextMessage) receivedMessage;
            System.out.println("收到消息: " + textMessage.getText());
        }

        // 关闭连接
        connection.close();
        System.out.println("ActiveMQ测试完成！");
    }
}
EOF

# 编译和运行测试（需要ActiveMQ客户端JAR包）
# javac -cp "/usr/local/activemq/lib/*" ActiveMQTest.java
# java -cp ".:/usr/local/activemq/lib/*" ActiveMQTest
```

---

## 8. ARM架构和麒麟系统特殊注意事项

### 8.1 ARM64架构优化

#### 8.1.1 JVM参数调优
```bash
# ARM64特有的JVM优化参数
export ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:+UnlockExperimentalVMOptions"
export ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:+UseZGC"  # 如果Java版本支持
export ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:+UseLargePages"
export ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:LargePageSizeInBytes=2m"
```

#### 8.1.2 网络优化
```bash
# 优化网络参数
echo 'net.core.rmem_max = 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 87380 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 134217728' | sudo tee -a /etc/sysctl.conf

# 应用网络参数
sudo sysctl -p
```

### 8.2 麒麟系统特有配置

#### 8.2.1 防火墙配置
```bash
# 开放ActiveMQ端口
sudo firewall-cmd --permanent --add-port=61616/tcp  # JMS端口
sudo firewall-cmd --permanent --add-port=8161/tcp   # Web控制台端口
sudo firewall-cmd --permanent --add-port=5672/tcp   # AMQP端口
sudo firewall-cmd --permanent --add-port=61613/tcp  # STOMP端口
sudo firewall-cmd --permanent --add-port=1883/tcp   # MQTT端口
sudo firewall-cmd --permanent --add-port=61614/tcp  # WebSocket端口
sudo firewall-cmd --permanent --add-port=1099/tcp   # JMX端口

# 重新加载防火墙规则
sudo firewall-cmd --reload

# 查看开放的端口
sudo firewall-cmd --list-ports
```

#### 8.2.2 SELinux配置
```bash
# 检查SELinux状态
getenforce

# 如果启用了SELinux，配置相关策略
sudo setsebool -P allow_java_execstack 1
sudo setsebool -P httpd_can_network_connect 1

# 为ActiveMQ目录设置SELinux上下文
sudo semanage fcontext -a -t bin_t "/opt/activemq/bin/activemq"
sudo restorecon -R /opt/activemq/
```

---

## 9. 常见问题排查

### 9.1 启动问题

#### 9.1.1 Java环境问题
```bash
# 错误：找不到Java
# 解决方案：检查JAVA_HOME设置
echo $JAVA_HOME
which java

# 修复Java环境
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export PATH=$JAVA_HOME/bin:$PATH
```

#### 9.1.2 权限问题
```bash
# 错误：权限不足
# 解决方案：修复文件权限
sudo chmod -R 755 /usr/local/activemq
sudo chmod -R 755 /var/log/activemq
sudo chmod -R 755 /var/lib/activemq
sudo chmod +x /usr/local/activemq/bin/activemq
```

#### 9.1.3 端口占用问题
```bash
# 检查端口占用
sudo lsof -i :61616
sudo lsof -i :8161

# 杀死占用端口的进程
sudo kill -9 <PID>

# 或者修改ActiveMQ配置使用其他端口
```

### 9.2 连接问题

#### 9.2.1 网络连接失败
```bash
# 检查网络连通性
telnet localhost 61616

# 检查防火墙设置
sudo firewall-cmd --list-ports

# 检查ActiveMQ监听地址
netstat -tuln | grep 61616
```

#### 9.2.2 认证失败
```bash
# 检查用户配置
cat /usr/local/activemq/conf/users.properties
cat /usr/local/activemq/conf/groups.properties

# 重置管理员密码
sudo vim /usr/local/activemq/conf/users.properties
# 修改：admin=newpassword
```

### 9.3 性能问题

#### 9.3.1 内存不足
```bash
# 检查内存使用
free -h
ps aux | grep activemq

# 调整JVM内存参数
sudo vim /usr/local/activemq/bin/env
# 修改：ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -Xms2G -Xmx4G"
```

#### 9.3.2 磁盘空间不足
```bash
# 检查磁盘使用
df -h
du -sh /usr/local/activemq/data/

# 清理日志文件
sudo find /usr/local/activemq/data/ -name "*.log" -mtime +7 -delete
```

---

## 10. 性能优化建议

### 10.1 JVM调优
```bash
# 针对ARM64架构的JVM优化参数
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -server"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -Xms2G -Xmx4G"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:+UseG1GC"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:MaxGCPauseMillis=200"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:+UseStringDeduplication"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:+OptimizeStringConcat"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:+UseCompressedOops"
ACTIVEMQ_OPTS="$ACTIVEMQ_OPTS -XX:+UseCompressedClassPointers"
```

### 10.2 ActiveMQ配置优化
```xml
<!-- 在activemq.xml中优化配置 -->
<systemUsage>
    <systemUsage>
        <memoryUsage>
            <memoryUsage percentOfJvmHeap="70" />
        </memoryUsage>
        <storeUsage>
            <storeUsage limit="100 gb"/>
        </storeUsage>
        <tempUsage>
            <tempUsage limit="50 gb"/>
        </tempUsage>
    </systemUsage>
</systemUsage>

<!-- 优化传输连接器 -->
<transportConnector name="openwire"
    uri="tcp://0.0.0.0:61616?maximumConnections=1000&amp;wireFormat.maxFrameSize=104857600&amp;wireFormat.maxInactivityDuration=30000"/>
```

### 10.3 操作系统优化
```bash
# 增加文件描述符限制
echo 'activemq soft nofile 65536' | sudo tee -a /etc/security/limits.conf
echo 'activemq hard nofile 65536' | sudo tee -a /etc/security/limits.conf

# 优化内核参数
echo 'vm.swappiness = 1' | sudo tee -a /etc/sysctl.conf
echo 'vm.dirty_ratio = 15' | sudo tee -a /etc/sysctl.conf
echo 'vm.dirty_background_ratio = 5' | sudo tee -a /etc/sysctl.conf

# 应用内核参数
sudo sysctl -p
```

### 10.4 监控和维护
```bash
# 设置日志轮转
sudo tee /etc/logrotate.d/activemq > /dev/null <<EOF
/usr/local/activemq/data/activemq.log {
    daily
    missingok
    rotate 7
    compress
    notifempty
    create 644 root root
    postrotate
        /bin/kill -USR1 \$(cat /usr/local/activemq/data/activemq.pid 2>/dev/null) 2>/dev/null || true
    endscript
}
EOF

# 创建监控脚本
sudo tee /usr/local/bin/activemq-monitor.sh > /dev/null <<'EOF'
#!/bin/bash
# ActiveMQ监控脚本

# 检查ActiveMQ进程
if ! pgrep -f "activemq" > /dev/null; then
    echo "$(date): ActiveMQ进程未运行，尝试重启..."
    systemctl restart activemq
fi

# 检查端口监听
if ! netstat -tuln | grep ":61616" > /dev/null; then
    echo "$(date): ActiveMQ端口61616未监听，尝试重启..."
    systemctl restart activemq
fi

# 检查内存使用
MEMORY_USAGE=$(ps aux | grep activemq | grep -v grep | awk '{print $4}')
if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
    echo "$(date): ActiveMQ内存使用率过高: ${MEMORY_USAGE}%"
fi
EOF

sudo chmod +x /usr/local/bin/activemq-monitor.sh

# 添加到crontab
echo "*/5 * * * * /usr/local/bin/activemq-monitor.sh >> /var/log/activemq-monitor.log 2>&1" | sudo crontab -
```

---

## 总结

本教程提供了在ARM64架构的银河麒麟V10 SP3系统上安装和配置Apache ActiveMQ的完整指南。主要包括：

1. **完整安装流程**：从系统准备到服务配置的详细步骤
2. **Spring Boot集成**：提供了完整的配置示例和代码
3. **ARM64优化**：针对ARM架构的JVM和系统参数调优
4. **安全配置**：用户权限、防火墙和SELinux配置
5. **问题排查**：常见问题的诊断和解决方案
6. **性能优化**：生产环境的调优建议

**重要提醒：**
- 确保Java 8或更高版本已正确安装
- 根据实际负载调整JVM内存参数
- 定期监控ActiveMQ运行状态和性能指标
- 及时更新ActiveMQ版本以获得安全补丁

如有问题，请参考官方文档或寻求技术支持。
