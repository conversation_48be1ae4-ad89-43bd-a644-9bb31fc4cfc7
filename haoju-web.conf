# Nginx配置文件 - 豪居Web项目
# 文件位置: /etc/nginx/conf.d/haoju-web.conf
# 
# 端口映射说明:
# - 外部端口8081 -> 内部端口80 (HTTP)
# - 外部端口8082 -> 内部端口443 (HTTPS)
# 
# 项目结构:
# - 网站根目录: /opt/haoju/web/
# - Vue项目1: /opt/haoju/web/new1/
# - Vue项目2: /opt/haoju/web/new2/
# - API服务: localhost:6007

# 上游API服务器配置
upstream haoju_api {
    server 127.0.0.1:6007;
    keepalive 32;
}

# HTTPS服务器配置 (监听443端口)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name your-domain.com www.your-domain.com;  # 请替换为您的实际域名
    
    # 网站根目录
    root /opt/haoju/web;
    index index.html index.htm;
    
    # SSL证书配置 (请取消注释并填入实际路径)
    # ssl_certificate /path/to/your/certificate.crt;
    # ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # 安全头配置
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 隐藏Nginx版本信息
    server_tokens off;
    
    # 访问日志配置
    access_log /var/log/nginx/haoju_access.log combined;
    error_log /var/log/nginx/haoju_error.log warn;
    
    # API代理配置 - 处理后端API请求
    location /api/ {
        proxy_pass http://haoju_api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 跨域配置
        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        add_header Access-Control-Expose-Headers "Content-Length,Content-Range" always;
        
        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin * always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
        
        # 代理超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Vue项目1配置 - new1
    location /new1/ {
        alias /opt/haoju/web/new1/;
        try_files $uri $uri/ /new1/index.html;
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
    }
    
    # Vue项目2配置 - new2
    location /new2/ {
        alias /opt/haoju/web/new2/;
        try_files $uri $uri/ /new2/index.html;
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
    }
    
    # 根路径配置 - 可以重定向到其中一个项目或显示选择页面
    location = / {
        # 选项1: 重定向到new1项目
        return 301 /new1/;
        
        # 选项2: 显示项目选择页面 (取消上面的注释，启用下面的配置)
        # try_files /index.html =404;
    }
    
    # 处理根目录下的其他请求
    location / {
        try_files $uri $uri/ =404;
    }
    
    # 安全配置 - 拒绝访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 拒绝访问备份文件
    location ~* \.(bak|backup|old|orig|original|tmp)$ {
        deny all;
    }
    
    # Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        font/opentype;
}

# HTTP服务器配置 (监听80端口) - 强制重定向到HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name your-domain.com www.your-domain.com;  # 请替换为您的实际域名
    
    # 隐藏Nginx版本信息
    server_tokens off;
    
    # 访问日志
    access_log /var/log/nginx/haoju_http_access.log combined;
    
    # 强制301重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

# 默认服务器配置 - 处理未匹配的域名
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    listen 443 ssl default_server;
    listen [::]:443 ssl default_server;
    
    server_name _;
    
    # SSL证书配置 (默认服务器也需要证书)
    # ssl_certificate /path/to/your/certificate.crt;
    # ssl_certificate_key /path/to/your/private.key;
    
    # 返回444状态码 (关闭连接)
    return 444;
}
