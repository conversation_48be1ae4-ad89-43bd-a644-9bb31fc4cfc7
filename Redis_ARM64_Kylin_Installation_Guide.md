# Redis 在 ARM64 银河麒麟 V10 SP3 系统安装教程

## 目录
1. [系统环境检查和准备工作](#1-系统环境检查和准备工作)
2. [Redis下载和安装](#2-redis下载和安装)
3. [配置文件设置和优化](#3-配置文件设置和优化)
4. [服务启动和管理](#4-服务启动和管理)
5. [安全配置](#5-安全配置)
6. [集群配置](#6-集群配置)
7. [验证安装](#7-验证安装)
8. [ARM架构和麒麟系统特殊注意事项](#8-arm架构和麒麟系统特殊注意事项)
9. [常见问题排查](#9-常见问题排查)
10. [性能调优和监控](#10-性能调优和监控)

---

## 1. 系统环境检查和准备工作

### 1.1 检查系统信息
```bash
# 检查操作系统版本
cat /etc/os-release

# 检查处理器架构
uname -m

# 检查内核版本
uname -r

# 检查可用内存
free -h

# 检查磁盘空间
df -h
```

**预期输出示例：**
- 架构应显示：`aarch64`
- 操作系统：`Kylin Linux V10 SP3`

### 1.2 更新系统包
```bash
# 更新包管理器缓存
sudo yum update -y

# 安装必要的开发工具和依赖
sudo yum groupinstall -y "Development Tools"
sudo yum install -y wget curl vim tar gcc gcc-c++ make tcl-devel
```

### 1.3 检查端口占用
```bash
# 检查Redis默认端口是否被占用
# 6379 - Redis默认端口
# 16379 - Redis集群总线端口
netstat -tuln | grep -E "(6379|16379)"

# 如果端口被占用，查看占用进程
sudo lsof -i :6379
sudo lsof -i :16379
```

---

## 2. Redis下载和安装

### 2.1 方法一：使用YUM包管理器安装（推荐）

#### 2.1.1 安装EPEL仓库
```bash
# 安装EPEL仓库
sudo yum install -y epel-release

# 更新包缓存
sudo yum update -y

# 搜索Redis包
yum search redis
```

#### 2.1.2 安装Redis
```bash
# 安装Redis服务器和工具
sudo yum install -y redis

# 验证安装
redis-server --version
redis-cli --version
```

### 2.2 方法二：编译安装（获取最新版本）

#### 2.2.1 创建Redis用户和目录
```bash
# 创建Redis专用用户
sudo useradd -r -s /bin/false redis

# 创建必要目录
sudo mkdir -p /opt/redis
sudo mkdir -p /var/lib/redis
sudo mkdir -p /var/log/redis
sudo mkdir -p /etc/redis

# 设置目录权限
sudo chown redis:redis /var/lib/redis
sudo chown redis:redis /var/log/redis
```

#### 2.2.2 下载和编译Redis
```bash
# 创建下载目录
mkdir -p ~/redis-install && cd ~/redis-install

# 下载Redis 7.2.4（最新稳定版本）
# 如果需要使用代理，设置代理环境变量
export http_proxy=http://127.0.0.1:7897
export https_proxy=http://127.0.0.1:7897

wget https://download.redis.io/redis-stable.tar.gz

# 验证下载文件
ls -la redis-stable.tar.gz

# 解压Redis源码
tar -xzf redis-stable.tar.gz
cd redis-stable

# 编译Redis（针对ARM64优化）
make PREFIX=/opt/redis install

# 验证编译结果
ls -la /opt/redis/bin/
```

#### 2.2.3 安装和配置
```bash
# 复制配置文件
sudo cp redis.conf /etc/redis/redis.conf

# 复制启动脚本
sudo cp utils/redis_init_script /etc/init.d/redis

# 设置目录权限
sudo chown -R redis:redis /opt/redis
sudo chown redis:redis /etc/redis/redis.conf

# 创建符号链接
sudo ln -s /opt/redis/bin/redis-server /usr/local/bin/redis-server
sudo ln -s /opt/redis/bin/redis-cli /usr/local/bin/redis-cli
sudo ln -s /opt/redis/bin/redis-benchmark /usr/local/bin/redis-benchmark
```

---

## 3. 配置文件设置和优化

### 3.1 主配置文件设置
```bash
# 备份原始配置文件
sudo cp /etc/redis/redis.conf /etc/redis/redis.conf.backup

# 编辑Redis配置文件
sudo vim /etc/redis/redis.conf
```

**关键配置项：**
```bash
# 基本配置
bind 127.0.0.1 ::1                    # 绑定地址（生产环境建议修改）
port 6379                             # 监听端口
timeout 300                           # 客户端空闲超时时间
tcp-keepalive 300                     # TCP keepalive时间

# 通用配置
daemonize yes                         # 后台运行
pidfile /var/run/redis/redis.pid      # PID文件路径
loglevel notice                       # 日志级别
logfile /var/log/redis/redis.log      # 日志文件路径

# 数据持久化配置
dir /var/lib/redis                    # 数据目录
dbfilename dump.rdb                   # RDB文件名

# RDB持久化配置
save 900 1                            # 900秒内至少1个key变化时保存
save 300 10                           # 300秒内至少10个key变化时保存
save 60 10000                         # 60秒内至少10000个key变化时保存
stop-writes-on-bgsave-error yes       # RDB保存失败时停止写入
rdbcompression yes                    # 启用RDB压缩
rdbchecksum yes                       # 启用RDB校验

# AOF持久化配置
appendonly yes                        # 启用AOF
appendfilename "appendonly.aof"       # AOF文件名
appendfsync everysec                  # AOF同步策略
no-appendfsync-on-rewrite no          # 重写时不同步
auto-aof-rewrite-percentage 100       # AOF重写触发百分比
auto-aof-rewrite-min-size 64mb        # AOF重写最小文件大小

# 内存管理
maxmemory 2gb                         # 最大内存限制
maxmemory-policy allkeys-lru          # 内存淘汰策略

# 网络配置
tcp-backlog 511                       # TCP监听队列长度
timeout 0                             # 客户端超时时间（0表示禁用）
tcp-keepalive 300                     # TCP keepalive

# 安全配置
# requirepass your_password_here       # 设置密码（取消注释并设置密码）
# rename-command FLUSHDB ""            # 禁用危险命令
# rename-command FLUSHALL ""
# rename-command CONFIG ""
```

### 3.2 系统限制配置
```bash
# 配置系统限制
sudo tee /etc/security/limits.d/redis.conf > /dev/null <<EOF
redis soft nofile 65535
redis hard nofile 65535
redis soft nproc 65535
redis hard nproc 65535
EOF

# 配置内核参数
sudo tee -a /etc/sysctl.conf > /dev/null <<EOF
# Redis优化参数
vm.overcommit_memory = 1
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
EOF

# 应用内核参数
sudo sysctl -p
```

### 3.3 创建systemd服务文件
```bash
# 创建Redis服务文件
sudo tee /etc/systemd/system/redis.service > /dev/null <<EOF
[Unit]
Description=Redis In-Memory Data Store
After=network.target

[Service]
Type=notify
User=redis
Group=redis
ExecStart=/opt/redis/bin/redis-server /etc/redis/redis.conf
ExecStop=/opt/redis/bin/redis-cli shutdown
Restart=always
RestartSec=3
LimitNOFILE=65535

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/var/lib/redis
ReadWritePaths=/var/log/redis
ReadWritePaths=/var/run/redis

[Install]
WantedBy=multi-user.target
EOF
```

---

## 4. 服务启动和管理

### 4.1 启动和管理Redis服务
```bash
# 创建运行时目录
sudo mkdir -p /var/run/redis
sudo chown redis:redis /var/run/redis

# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动Redis服务
sudo systemctl start redis

# 检查服务状态
sudo systemctl status redis

# 设置开机自启动
sudo systemctl enable redis

# 其他管理命令
sudo systemctl stop redis      # 停止服务
sudo systemctl restart redis   # 重启服务
sudo systemctl disable redis   # 禁用开机自启动
```

### 4.2 手动启动和停止（可选）
```bash
# 手动启动Redis
sudo -u redis /opt/redis/bin/redis-server /etc/redis/redis.conf

# 手动停止Redis
sudo -u redis /opt/redis/bin/redis-cli shutdown

# 查看Redis进程
ps aux | grep redis

# 检查Redis日志
tail -f /var/log/redis/redis.log
```

---

## 5. 安全配置

### 5.1 设置密码认证
```bash
# 编辑配置文件
sudo vim /etc/redis/redis.conf

# 取消注释并设置密码
requirepass your_strong_password_here

# 重启Redis服务
sudo systemctl restart redis

# 测试密码认证
redis-cli
127.0.0.1:6379> AUTH your_strong_password_here
OK
```

### 5.2 网络安全配置
```bash
# 限制绑定地址（仅本地访问）
bind 127.0.0.1 ::1

# 如果需要远程访问，指定具体IP
# bind 127.0.0.1 *************

# 启用保护模式
protected-mode yes

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command EVAL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_9a8b7c6d5e4f"
```

### 5.3 防火墙配置
```bash
# 开放Redis端口（仅在需要远程访问时）
sudo firewall-cmd --permanent --add-port=6379/tcp

# 如果配置集群，还需要开放集群总线端口
sudo firewall-cmd --permanent --add-port=16379/tcp

# 重新加载防火墙规则
sudo firewall-cmd --reload

# 查看开放的端口
sudo firewall-cmd --list-ports
```

---

## 6. 集群配置

### 6.1 Redis Cluster配置

#### 6.1.1 准备集群节点
```bash
# 创建集群配置目录
sudo mkdir -p /etc/redis/cluster
sudo mkdir -p /var/lib/redis/cluster

# 为每个节点创建配置文件（示例：6个节点）
for port in 7000 7001 7002 7003 7004 7005; do
    sudo tee /etc/redis/cluster/redis-${port}.conf > /dev/null <<EOF
port ${port}
cluster-enabled yes
cluster-config-file nodes-${port}.conf
cluster-node-timeout 15000
appendonly yes
dir /var/lib/redis/cluster/${port}
pidfile /var/run/redis/redis-${port}.pid
logfile /var/log/redis/redis-${port}.log
bind 127.0.0.1
EOF

    # 创建数据目录
    sudo mkdir -p /var/lib/redis/cluster/${port}
    sudo chown redis:redis /var/lib/redis/cluster/${port}
done
```

#### 6.1.2 启动集群节点
```bash
# 为每个节点创建systemd服务
for port in 7000 7001 7002 7003 7004 7005; do
    sudo tee /etc/systemd/system/redis-${port}.service > /dev/null <<EOF
[Unit]
Description=Redis Cluster Node ${port}
After=network.target

[Service]
Type=notify
User=redis
Group=redis
ExecStart=/opt/redis/bin/redis-server /etc/redis/cluster/redis-${port}.conf
Restart=always
RestartSec=3
LimitNOFILE=65535

[Install]
WantedBy=multi-user.target
EOF

    # 启动节点
    sudo systemctl enable redis-${port}
    sudo systemctl start redis-${port}
done
```

#### 6.1.3 创建集群
```bash
# 使用redis-cli创建集群
/opt/redis/bin/redis-cli --cluster create \
    127.0.0.1:7000 127.0.0.1:7001 127.0.0.1:7002 \
    127.0.0.1:7003 127.0.0.1:7004 127.0.0.1:7005 \
    --cluster-replicas 1

# 验证集群状态
/opt/redis/bin/redis-cli -c -p 7000 cluster nodes
/opt/redis/bin/redis-cli -c -p 7000 cluster info
```

### 6.2 Redis Sentinel配置（高可用）

#### 6.2.1 配置Sentinel
```bash
# 创建Sentinel配置文件
sudo tee /etc/redis/sentinel.conf > /dev/null <<EOF
port 26379
sentinel monitor mymaster 127.0.0.1 6379 2
sentinel auth-pass mymaster your_password_here
sentinel down-after-milliseconds mymaster 30000
sentinel parallel-syncs mymaster 1
sentinel failover-timeout mymaster 180000
sentinel deny-scripts-reconfig yes
logfile /var/log/redis/sentinel.log
EOF

# 设置权限
sudo chown redis:redis /etc/redis/sentinel.conf
```

#### 6.2.2 启动Sentinel服务
```bash
# 创建Sentinel systemd服务
sudo tee /etc/systemd/system/redis-sentinel.service > /dev/null <<EOF
[Unit]
Description=Redis Sentinel
After=network.target

[Service]
Type=notify
User=redis
Group=redis
ExecStart=/opt/redis/bin/redis-sentinel /etc/redis/sentinel.conf
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 启动Sentinel
sudo systemctl enable redis-sentinel
sudo systemctl start redis-sentinel
sudo systemctl status redis-sentinel
```

---

## 7. 验证安装

### 7.1 基本连接测试
```bash
# 检查Redis进程
ps aux | grep redis

# 检查端口监听状态
netstat -tuln | grep 6379

# 检查Redis日志
tail -f /var/log/redis/redis.log

# 测试Redis连接
redis-cli ping
```

### 7.2 功能测试
```bash
# 连接到Redis
redis-cli

# 基本操作测试
127.0.0.1:6379> SET test_key "Hello Redis"
OK
127.0.0.1:6379> GET test_key
"Hello Redis"
127.0.0.1:6379> DEL test_key
(integer) 1
127.0.0.1:6379> EXISTS test_key
(integer) 0

# 测试数据类型
127.0.0.1:6379> LPUSH test_list "item1" "item2" "item3"
(integer) 3
127.0.0.1:6379> LRANGE test_list 0 -1
1) "item3"
2) "item2"
3) "item1"

# 测试哈希
127.0.0.1:6379> HSET test_hash field1 "value1" field2 "value2"
(integer) 2
127.0.0.1:6379> HGETALL test_hash
1) "field1"
2) "value1"
3) "field2"
4) "value2"

# 退出
127.0.0.1:6379> QUIT
```

### 7.3 性能测试
```bash
# 使用redis-benchmark进行性能测试
redis-benchmark -h 127.0.0.1 -p 6379 -n 10000 -c 50

# 测试特定操作
redis-benchmark -h 127.0.0.1 -p 6379 -t set,get -n 10000 -q

# 测试大数据量
redis-benchmark -h 127.0.0.1 -p 6379 -n 100000 -d 1024 -q
```

### 7.4 持久化验证
```bash
# 检查RDB文件
ls -la /var/lib/redis/dump.rdb

# 检查AOF文件
ls -la /var/lib/redis/appendonly.aof

# 手动触发RDB保存
redis-cli BGSAVE

# 检查最后保存时间
redis-cli LASTSAVE
```

---

## 8. ARM架构和麒麟系统特殊注意事项

### 8.1 ARM64架构优化

#### 8.1.1 编译优化
```bash
# 针对ARM64的编译优化参数
export CFLAGS="-O3 -march=armv8-a -mtune=cortex-a72"
export CXXFLAGS="-O3 -march=armv8-a -mtune=cortex-a72"

# 重新编译Redis（如果使用源码安装）
make clean
make PREFIX=/opt/redis install
```

#### 8.1.2 内存优化
```bash
# ARM64特有的内存优化配置
echo 'vm.swappiness = 1' | sudo tee -a /etc/sysctl.conf
echo 'vm.dirty_ratio = 15' | sudo tee -a /etc/sysctl.conf
echo 'vm.dirty_background_ratio = 5' | sudo tee -a /etc/sysctl.conf
echo 'vm.overcommit_memory = 1' | sudo tee -a /etc/sysctl.conf

# 应用配置
sudo sysctl -p
```

#### 8.1.3 CPU亲和性设置
```bash
# 设置Redis进程CPU亲和性
# 编辑systemd服务文件
sudo vim /etc/systemd/system/redis.service

# 添加CPU亲和性设置
[Service]
CPUAffinity=0-3  # 绑定到前4个CPU核心
```

### 8.2 麒麟系统特有配置

#### 8.2.1 防火墙配置
```bash
# 开放Redis端口
sudo firewall-cmd --permanent --add-port=6379/tcp

# 如果配置集群，开放集群端口
sudo firewall-cmd --permanent --add-port=16379/tcp

# 如果配置Sentinel，开放Sentinel端口
sudo firewall-cmd --permanent --add-port=26379/tcp

# 重新加载防火墙规则
sudo firewall-cmd --reload

# 查看开放的端口
sudo firewall-cmd --list-ports
```

#### 8.2.2 SELinux配置
```bash
# 检查SELinux状态
getenforce

# 如果启用了SELinux，配置相关策略
sudo setsebool -P redis_enable_notify on

# 为Redis目录设置SELinux上下文
sudo semanage fcontext -a -t redis_exec_t "/opt/redis/bin/redis-server"
sudo semanage fcontext -a -t redis_var_lib_t "/var/lib/redis(/.*)?"
sudo semanage fcontext -a -t redis_log_t "/var/log/redis(/.*)?"
sudo restorecon -R /opt/redis/
sudo restorecon -R /var/lib/redis/
sudo restorecon -R /var/log/redis/
```

---

## 9. 常见问题排查

### 9.1 启动问题

#### 9.1.1 端口占用问题
```bash
# 错误：Address already in use
# 解决方案：检查端口占用
sudo lsof -i :6379
sudo netstat -tuln | grep 6379

# 杀死占用端口的进程
sudo kill -9 <PID>

# 或者修改Redis配置使用其他端口
sudo vim /etc/redis/redis.conf
# 修改：port 6380
```

#### 9.1.2 权限问题
```bash
# 错误：Permission denied
# 解决方案：修复文件权限
sudo chown -R redis:redis /var/lib/redis
sudo chown -R redis:redis /var/log/redis
sudo chown redis:redis /etc/redis/redis.conf
sudo chmod 640 /etc/redis/redis.conf
```

#### 9.1.3 内存不足问题
```bash
# 错误：Cannot allocate memory
# 解决方案：调整内存设置
echo 'vm.overcommit_memory = 1' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 或者减少maxmemory设置
sudo vim /etc/redis/redis.conf
# 修改：maxmemory 1gb
```

### 9.2 连接问题

#### 9.2.1 网络连接失败
```bash
# 检查Redis是否运行
sudo systemctl status redis

# 检查绑定地址
grep "^bind" /etc/redis/redis.conf

# 测试本地连接
redis-cli ping

# 测试远程连接
telnet <redis-server-ip> 6379
```

#### 9.2.2 认证失败
```bash
# 检查密码配置
grep "^requirepass" /etc/redis/redis.conf

# 使用密码连接
redis-cli -a your_password

# 或者在连接后认证
redis-cli
127.0.0.1:6379> AUTH your_password
```

### 9.3 性能问题

#### 9.3.1 响应慢
```bash
# 检查慢查询日志
redis-cli SLOWLOG GET 10

# 检查内存使用
redis-cli INFO memory

# 检查连接数
redis-cli INFO clients

# 检查命令统计
redis-cli INFO commandstats
```

#### 9.3.2 内存使用过高
```bash
# 分析内存使用
redis-cli INFO memory
redis-cli MEMORY USAGE <key>

# 检查大key
redis-cli --bigkeys

# 设置内存淘汰策略
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

---

## 10. 性能调优和监控

### 10.1 性能调优配置
```bash
# 编辑Redis配置文件
sudo vim /etc/redis/redis.conf

# 关键性能参数
maxmemory 4gb                         # 根据系统内存调整
maxmemory-policy allkeys-lru          # 内存淘汰策略
tcp-backlog 511                       # TCP监听队列
timeout 300                           # 客户端超时
tcp-keepalive 300                     # TCP keepalive
databases 16                          # 数据库数量

# 持久化优化
save 900 1                            # RDB保存策略
save 300 10
save 60 10000
appendonly yes                        # 启用AOF
appendfsync everysec                  # AOF同步策略
no-appendfsync-on-rewrite no          # 重写时同步策略
auto-aof-rewrite-percentage 100       # AOF重写触发条件
auto-aof-rewrite-min-size 64mb

# 网络优化
tcp-backlog 511
timeout 0
tcp-keepalive 300
```

### 10.2 系统级优化
```bash
# 内核参数优化
sudo tee -a /etc/sysctl.conf > /dev/null <<EOF
# Redis性能优化
vm.overcommit_memory = 1
vm.swappiness = 1
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_keepalive_probes = 3
net.ipv4.tcp_keepalive_intvl = 15
EOF

# 应用内核参数
sudo sysctl -p

# 文件描述符限制
echo 'redis soft nofile 65535' | sudo tee -a /etc/security/limits.conf
echo 'redis hard nofile 65535' | sudo tee -a /etc/security/limits.conf
```

### 10.3 监控脚本
```bash
# 创建Redis监控脚本
sudo tee /usr/local/bin/redis-monitor.sh > /dev/null <<'EOF'
#!/bin/bash
# Redis监控脚本

REDIS_CLI="/opt/redis/bin/redis-cli"
LOG_FILE="/var/log/redis-monitor.log"

# 检查Redis进程
if ! pgrep redis-server > /dev/null; then
    echo "$(date): Redis进程未运行，尝试重启..." >> $LOG_FILE
    systemctl restart redis
fi

# 检查Redis响应
if ! $REDIS_CLI ping > /dev/null 2>&1; then
    echo "$(date): Redis无响应，尝试重启..." >> $LOG_FILE
    systemctl restart redis
fi

# 检查内存使用
MEMORY_USAGE=$($REDIS_CLI INFO memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')
echo "$(date): Redis内存使用: $MEMORY_USAGE" >> $LOG_FILE

# 检查连接数
CONNECTED_CLIENTS=$($REDIS_CLI INFO clients | grep connected_clients | cut -d: -f2 | tr -d '\r')
echo "$(date): Redis连接数: $CONNECTED_CLIENTS" >> $LOG_FILE

# 检查慢查询
SLOW_QUERIES=$($REDIS_CLI SLOWLOG LEN)
if [ "$SLOW_QUERIES" -gt 0 ]; then
    echo "$(date): 发现 $SLOW_QUERIES 个慢查询" >> $LOG_FILE
fi
EOF

sudo chmod +x /usr/local/bin/redis-monitor.sh

# 添加到crontab
echo "*/5 * * * * /usr/local/bin/redis-monitor.sh" | sudo crontab -
```

### 10.4 日志轮转配置
```bash
# 配置Redis日志轮转
sudo tee /etc/logrotate.d/redis > /dev/null <<EOF
/var/log/redis/*.log {
    daily
    missingok
    rotate 7
    compress
    notifempty
    create 644 redis redis
    postrotate
        /bin/kill -USR1 \$(cat /var/run/redis/redis.pid 2>/dev/null) 2>/dev/null || true
    endscript
}
EOF
```

---

## 总结

本教程提供了在ARM64架构的银河麒麟V10 SP3系统上安装和配置Redis的完整指南。主要包括：

1. **多种安装方式**：YUM包管理器安装和源码编译安装
2. **完整配置**：基础配置、安全配置、集群配置和高可用配置
3. **ARM64优化**：针对ARM架构的编译和运行时优化
4. **安全加固**：密码认证、网络安全和系统安全配置
5. **集群部署**：Redis Cluster和Redis Sentinel配置
6. **问题排查**：常见问题的诊断和解决方案
7. **性能调优**：系统级和应用级的性能优化建议
8. **监控维护**：监控脚本和日志管理配置

**重要提醒：**
- 根据实际负载调整内存和连接数限制
- 生产环境务必设置密码认证
- 定期备份Redis数据和配置文件
- 监控Redis性能指标和系统资源使用
- 及时更新Redis版本以获得安全补丁

如有问题，请参考官方文档或寻求技术支持。
