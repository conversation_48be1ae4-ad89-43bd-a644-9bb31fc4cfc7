# 豪居Web项目Nginx部署指南

## 配置文件说明

### 文件位置
- 配置文件：`/etc/nginx/conf.d/haoju-web.conf`
- 项目目录：`/opt/haoju/web/`

### 端口映射架构
```
外部访问 -> 防火墙/路由器 -> 服务器内部
8081     -> 端口转发      -> 80 (HTTP)
8082     -> 端口转发      -> 443 (HTTPS)
```

## 部署步骤

### 1. 复制配置文件
```bash
# 将配置文件复制到Nginx配置目录
sudo cp haoju-web.conf /etc/nginx/conf.d/

# 设置正确的权限
sudo chown root:root /etc/nginx/conf.d/haoju-web.conf
sudo chmod 644 /etc/nginx/conf.d/haoju-web.conf
```

### 2. 修改配置文件中的域名
```bash
# 编辑配置文件
sudo vim /etc/nginx/conf.d/haoju-web.conf

# 将以下内容替换为您的实际域名：
# server_name your-domain.com www.your-domain.com;
# 例如：
# server_name haoju.example.com www.haoju.example.com;
```

### 3. 配置SSL证书
```bash
# 取消注释并修改SSL证书路径
# ssl_certificate /path/to/your/certificate.crt;
# ssl_certificate_key /path/to/your/private.key;

# 例如：
# ssl_certificate /etc/nginx/ssl/haoju.crt;
# ssl_certificate_key /etc/nginx/ssl/haoju.key;
```

### 4. 创建项目目录结构
```bash
# 创建项目根目录
sudo mkdir -p /opt/haoju/web

# 创建Vue项目目录
sudo mkdir -p /opt/haoju/web/new1
sudo mkdir -p /opt/haoju/web/new2

# 设置目录权限
sudo chown -R nginx:nginx /opt/haoju/web
sudo chmod -R 755 /opt/haoju/web
```

### 5. 部署Vue项目
```bash
# 将构建好的Vue项目文件复制到对应目录
# 项目1
sudo cp -r /path/to/your/vue-project1/dist/* /opt/haoju/web/new1/

# 项目2
sudo cp -r /path/to/your/vue-project2/dist/* /opt/haoju/web/new2/

# 确保index.html文件存在
ls -la /opt/haoju/web/new1/index.html
ls -la /opt/haoju/web/new2/index.html
```

### 6. 创建日志目录
```bash
# 确保日志目录存在
sudo mkdir -p /var/log/nginx

# 设置日志文件权限
sudo touch /var/log/nginx/haoju_access.log
sudo touch /var/log/nginx/haoju_error.log
sudo touch /var/log/nginx/haoju_http_access.log
sudo chown nginx:nginx /var/log/nginx/haoju_*.log
```

### 7. 验证配置并重启Nginx
```bash
# 检查配置文件语法
sudo nginx -t

# 如果语法正确，重新加载Nginx配置
sudo systemctl reload nginx

# 或者重启Nginx服务
sudo systemctl restart nginx

# 检查服务状态
sudo systemctl status nginx
```

## 访问方式

### 外部访问地址
- **HTTP访问**：`http://your-domain.com:8081` (会自动重定向到HTTPS)
- **HTTPS访问**：`https://your-domain.com:8082`

### 项目访问路径
- **Vue项目1**：`https://your-domain.com:8082/new1/`
- **Vue项目2**：`https://your-domain.com:8082/new2/`
- **API接口**：`https://your-domain.com:8082/api/`

## API配置说明

### 后端API服务
- **服务地址**：`127.0.0.1:6007`
- **代理路径**：`/api/`
- **跨域处理**：已配置CORS头

### Vue项目中的API调用
在Vue项目中，可以直接使用相对路径调用API：

```javascript
// 示例：在Vue项目中调用API
axios.get('/api/users')
  .then(response => {
    console.log(response.data);
  })
  .catch(error => {
    console.error('API调用失败:', error);
  });
```

## 安全配置

### 已包含的安全措施
- ✅ 隐藏Nginx版本信息
- ✅ 安全HTTP头配置
- ✅ SSL/TLS安全配置
- ✅ 拒绝访问敏感文件
- ✅ HSTS安全传输
- ✅ XSS防护
- ✅ 内容类型嗅探防护

### 防火墙配置建议
```bash
# 开放必要端口
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload

# 检查端口状态
sudo firewall-cmd --list-ports
```

## 故障排查

### 常见问题

**1. 404错误 - 页面无法访问**
```bash
# 检查文件是否存在
ls -la /opt/haoju/web/new1/index.html
ls -la /opt/haoju/web/new2/index.html

# 检查文件权限
sudo chown -R nginx:nginx /opt/haoju/web
sudo chmod -R 755 /opt/haoju/web
```

**2. API调用失败**
```bash
# 检查后端服务是否运行
sudo netstat -tlnp | grep :6007
curl http://127.0.0.1:6007/health  # 假设有健康检查接口

# 检查Nginx错误日志
sudo tail -f /var/log/nginx/haoju_error.log
```

**3. SSL证书问题**
```bash
# 检查证书文件
sudo ls -la /path/to/your/certificate.crt
sudo ls -la /path/to/your/private.key

# 验证证书
sudo openssl x509 -in /path/to/your/certificate.crt -text -noout
```

**4. 配置语法错误**
```bash
# 检查配置语法
sudo nginx -t

# 查看详细错误信息
sudo nginx -T
```

### 日志查看
```bash
# 查看访问日志
sudo tail -f /var/log/nginx/haoju_access.log

# 查看错误日志
sudo tail -f /var/log/nginx/haoju_error.log

# 查看Nginx主错误日志
sudo tail -f /var/log/nginx/error.log
```

## 性能优化建议

### 1. 启用Gzip压缩
配置文件中已包含Gzip压缩配置，可以显著减少传输数据量。

### 2. 静态资源缓存
配置文件中已设置静态资源缓存，提高加载速度。

### 3. 连接保持
已配置keepalive连接，减少连接建立开销。

## 维护建议

### 定期任务
- 定期检查SSL证书有效期
- 监控日志文件大小，配置日志轮转
- 定期更新Nginx版本
- 监控API服务状态

### 备份建议
- 备份Nginx配置文件
- 备份SSL证书文件
- 备份Vue项目文件

---

**注意事项：**
1. 请确保防火墙/路由器正确配置了端口转发
2. 请替换配置文件中的域名为实际域名
3. 请配置有效的SSL证书
4. 请确保后端API服务正常运行在6007端口
